package cn.iocoder.yudao.module.visitor.dal.mysql;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.visitor.controller.admin.training.vo.VisitorTrainingRecordPageReqVO;
import cn.iocoder.yudao.module.visitor.dal.dataobject.VisitorTrainingRecordDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * 访客培训完成记录 Mapper
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Mapper
public interface VisitorTrainingRecordMapper extends BaseMapperX<VisitorTrainingRecordDO> {

    default PageResult<VisitorTrainingRecordDO> selectPage(VisitorTrainingRecordPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<VisitorTrainingRecordDO>()
                .eqIfPresent(VisitorTrainingRecordDO::getApplicationId, reqVO.getApplicationId())
                .eqIfPresent(VisitorTrainingRecordDO::getTrainingId, reqVO.getTrainingId())
                .likeIfPresent(VisitorTrainingRecordDO::getVisitorName, reqVO.getVisitorName())
                .eqIfPresent(VisitorTrainingRecordDO::getCompletionStatus, reqVO.getCompletionStatus())
                .geIfPresent(VisitorTrainingRecordDO::getExamScore, reqVO.getMinExamScore())
                .leIfPresent(VisitorTrainingRecordDO::getExamScore, reqVO.getMaxExamScore())
                .betweenIfPresent(VisitorTrainingRecordDO::getStartTime, reqVO.getStartTime())
                .betweenIfPresent(VisitorTrainingRecordDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(VisitorTrainingRecordDO::getId));
    }

    default List<VisitorTrainingRecordDO> selectList(VisitorTrainingRecordPageReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<VisitorTrainingRecordDO>()
                .eqIfPresent(VisitorTrainingRecordDO::getApplicationId, reqVO.getApplicationId())
                .eqIfPresent(VisitorTrainingRecordDO::getTrainingId, reqVO.getTrainingId())
                .likeIfPresent(VisitorTrainingRecordDO::getVisitorName, reqVO.getVisitorName())
                .eqIfPresent(VisitorTrainingRecordDO::getCompletionStatus, reqVO.getCompletionStatus())
                .geIfPresent(VisitorTrainingRecordDO::getExamScore, reqVO.getMinExamScore())
                .leIfPresent(VisitorTrainingRecordDO::getExamScore, reqVO.getMaxExamScore())
                .betweenIfPresent(VisitorTrainingRecordDO::getStartTime, reqVO.getStartTime())
                .betweenIfPresent(VisitorTrainingRecordDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(VisitorTrainingRecordDO::getId));
    }

    default List<VisitorTrainingRecordDO> selectListByApplicationId(Long applicationId) {
        return selectList(VisitorTrainingRecordDO::getApplicationId, applicationId);
    }

    default List<VisitorTrainingRecordDO> selectListByApplicationIds(Collection<Long> applicationIds) {
        return selectList(new LambdaQueryWrapperX<VisitorTrainingRecordDO>()
                .inIfPresent(VisitorTrainingRecordDO::getApplicationId, applicationIds));
    }

    default List<VisitorTrainingRecordDO> selectListByTrainingId(Long trainingId) {
        return selectList(VisitorTrainingRecordDO::getTrainingId, trainingId);
    }

    default VisitorTrainingRecordDO selectByApplicationIdAndTrainingId(Long applicationId, Long trainingId) {
        return selectOne(new LambdaQueryWrapperX<VisitorTrainingRecordDO>()
                .eq(VisitorTrainingRecordDO::getApplicationId, applicationId)
                .eq(VisitorTrainingRecordDO::getTrainingId, trainingId));
    }

    default List<VisitorTrainingRecordDO> selectCompletedByApplicationId(Long applicationId) {
        return selectList(new LambdaQueryWrapperX<VisitorTrainingRecordDO>()
                .eq(VisitorTrainingRecordDO::getApplicationId, applicationId)
                .eq(VisitorTrainingRecordDO::getCompletionStatus, 1)); // 已完成?    }

    default List<VisitorTrainingRecordDO> selectListByCompletionStatus(Integer completionStatus) {
        return selectList(VisitorTrainingRecordDO::getCompletionStatus, completionStatus);
    }

    default List<VisitorTrainingRecordDO> selectExpiredRecords(LocalDateTime currentTime) {
        return selectList(new LambdaQueryWrapperX<VisitorTrainingRecordDO>()
                .eq(VisitorTrainingRecordDO::getCompletionStatus, 0) // 鏈畬鎴?                .lt(VisitorTrainingRecordDO::getStartTime, currentTime.minusHours(24))); // 瓒呰繃24灏忔椂鏈畬鎴?    }

    default List<VisitorTrainingRecordDO> selectListByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        return selectList(new LambdaQueryWrapperX<VisitorTrainingRecordDO>()
                .ge(VisitorTrainingRecordDO::getStartTime, startTime)
                .le(VisitorTrainingRecordDO::getStartTime, endTime));
    }

    default Long selectCompletedCountByApplicationId(Long applicationId) {
        return selectCount(new LambdaQueryWrapperX<VisitorTrainingRecordDO>()
                .eq(VisitorTrainingRecordDO::getApplicationId, applicationId)
                .eq(VisitorTrainingRecordDO::getCompletionStatus, 1));
    }

    default Long selectRequiredCompletedCountByApplicationId(Long applicationId, Collection<Long> requiredTrainingIds) {
        return selectCount(new LambdaQueryWrapperX<VisitorTrainingRecordDO>()
                .eq(VisitorTrainingRecordDO::getApplicationId, applicationId)
                .eq(VisitorTrainingRecordDO::getCompletionStatus, 1)
                .inIfPresent(VisitorTrainingRecordDO::getTrainingId, requiredTrainingIds));
    }

    /**
     * 统计培训记录鏁伴噺鎸夊畬鎴愮姸鎬佸垎缁?     */
    List<Object[]> selectCountGroupByCompletionStatus();

    /**
     * 统计培训记录鏁伴噺鎸夊煿璁璉D列嗙粍
     */
    List<Object[]> selectCountGroupByTrainingId();

    /**
     * 查询鎸囧畾时间鑼冨洿鍐呯殑培训完成统计
     */
    Long selectCompletedCountByTimeRange(@Param("startTime") LocalDateTime startTime, 
                                        @Param("endTime") LocalDateTime endTime);

    /**
     * 查询骞冲潎考试分数
     */
    Double selectAvgExamScore(@Param("trainingId") Long trainingId);

}

