package cn.iocoder.yudao.module.visitor.controller.admin.record.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 管理后台 - 访客进出记录创建 Request VO
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Schema(description = "管理后台 - 访客进出记录创建 Request VO")
@Data
public class VisitorRecordCreateReqVO {

    @Schema(description = "申请单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "申请单ID不能为空")
    private Long applicationId;

    @Schema(description = "访客姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotNull(message = "访客姓名不能为空")
    private String visitorName;

    @Schema(description = "访客电话", example = "13800138000")
    private String visitorPhone;

    @Schema(description = "操作类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "操作类型不能为空")
    private Integer operationType;

    @Schema(description = "操作时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "操作时间不能为空")
    private LocalDateTime operationTime;

    @Schema(description = "现场访客照片URL", example = "https://example.com/visitor.jpg")
    private String visitorPhoto;

    @Schema(description = "操作员ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "操作员ID不能为空")
    private Long operatorId;

    @Schema(description = "门岗位置", requiredMode = Schema.RequiredMode.REQUIRED, example = "东门")
    @NotNull(message = "门岗位置不能为空")
    private String gateLocation;

}
