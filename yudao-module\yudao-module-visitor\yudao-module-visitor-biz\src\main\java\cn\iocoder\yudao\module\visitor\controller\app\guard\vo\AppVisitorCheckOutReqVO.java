package cn.iocoder.yudao.module.visitor.controller.app.guard.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 用户 APP - 访客出园登记 Request VO
 *
 * <AUTHOR>
 * @since 2025-01-12
 */
@Schema(description = "用户 APP - 访客出园登记 Request VO")
@Data
public class AppVisitorCheckOutReqVO {

    @Schema(description = "申请ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "申请ID不能为空")
    private Long applicationId;

    @Schema(description = "门岗浣嶇疆", example = "涓滈棬")
    private String gateLocation;

    @Schema(description = "车牌鍙?, example = "绮12345")
    private String vehiclePlate;

    @Schema(description = "车辆照片URL", example = "https://example.com/vehicle.jpg")
    private String vehiclePhoto;

    @Schema(description = "鐜板満访客照片URL", example = "https://example.com/visitor.jpg")
    private String visitorPhoto;

    @Schema(description = "异常鎯呭喌描述", example = "鏃犲紓甯?)
    private String abnormalInfo;

    @Schema(description = "异常照片URL鏁扮粍")
    private List<String> abnormalPhotos;

    @Schema(description = "备注信息", example = "正常出园")
    private String remarks;

}

