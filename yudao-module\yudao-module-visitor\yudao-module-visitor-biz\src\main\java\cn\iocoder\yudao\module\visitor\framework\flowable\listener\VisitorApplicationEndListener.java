package cn.iocoder.yudao.module.visitor.framework.flowable.listener;

import cn.iocoder.yudao.module.visitor.service.application.VisitorApplicationService;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

/**
 * 访客申请流程结束鐩戝惉鍣? *
 * <AUTHOR>
 * @since 2025-01-12
 */
@Component("visitorApplicationEndListener")
@Slf4j
public class VisitorApplicationEndListener implements ExecutionListener {

    @Resource
    private VisitorApplicationService visitorApplicationService;

    @Override
    public void notify(DelegateExecution execution) {
        log.info("[notify] 访客申请流程结束锛屾祦绋嬪疄渚婭D锛歿}", execution.getProcessInstanceId());
        
        try {
            // 鑾峰彇流程鍙橀噺
            Object applicationIdObj = execution.getVariable("applicationId");
            Object approvalResultObj = execution.getVariable("approvalResult");
            
            if(applicationIdObj != null) {
                Long applicationId = Long.valueOf(applicationIdObj.toString());
                Boolean approved = Boolean.valueOf(approvalResultObj != null ? approvalResultObj.toString() : "false");
                
                log.info("[notify] 申请ID锛歿}锛屽鎵圭粨鏋滐細{}", applicationId, approved);
                
                // 树规嵁瀹℃壒结果更新申请状态?                if(approved) {
                    // 瀹℃壒通过锛岀敓鎴愪簩缁寸爜
                    visitorApplicationService.generateVisitorQrCode(applicationId);
                    log.info("[notify] 瀹℃壒通过锛屽凡生成二维码?);
                } else {
                    // 瀹℃壒驳回
                    String rejectReason = (String) execution.getVariable("rejectReason");
                    log.info("[notify] 瀹℃壒驳回锛屽師图狅細{}", rejectReason);
                }
            }

            log.info("[notify] 访客申请流程结束鐩戝惉鍣ㄦ墽表屽畬鎴?);

        } catch(Exception e) {
            log.error("[notify] 访客申请流程结束鐩戝惉鍣ㄦ墽表屽け璐?, e);
            throw new RuntimeException("流程结束鐩戝惉鍣ㄦ墽表屽け璐?, e);
        }
    }

}

