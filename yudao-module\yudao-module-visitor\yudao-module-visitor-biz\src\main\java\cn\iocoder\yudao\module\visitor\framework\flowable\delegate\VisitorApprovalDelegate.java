package cn.iocoder.yudao.module.visitor.framework.flowable.delegate;

import cn.iocoder.yudao.module.visitor.dal.dataobject.VisitorApplicationDO;
import cn.iocoder.yudao.module.visitor.framework.notification.VisitorNotificationService;
import cn.iocoder.yudao.module.visitor.service.application.VisitorApplicationService;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

/**
 * 访客申请瀹℃壒濮旀墭类? *
 * <AUTHOR>
 * @since 2025-01-12
 */
@Component("visitorApprovalDelegate")
@Slf4j
public class VisitorApprovalDelegate implements JavaDelegate {

    @Resource
    private VisitorApplicationService visitorApplicationService;

    @Resource
    private VisitorNotificationService visitorNotificationService;

    @Override
    public void execute(DelegateExecution execution) {
        log.info("[execute] 执行访客申请瀹℃壒濮旀墭锛屾祦绋嬪疄渚婭D锛歿}", execution.getProcessInstanceId());
        
        try {
            // 鑾峰彇娴佺▼鍙橀噺
            Object applicationIdObj = execution.getVariable("applicationId");
            Object approvalResultObj = execution.getVariable("approvalResult");
            String approvalReason = (String) execution.getVariable("approvalReason");
            Object approverIdObj = execution.getVariable("approverId");
            
            if(applicationIdObj != null && approvalResultObj != null) {
                Long applicationId = Long.valueOf(applicationIdObj.toString());
                Boolean approved = Boolean.valueOf(approvalResultObj.toString());
                Long approverId = approverIdObj != null ? Long.valueOf(approverIdObj.toString()) : null;
                
                log.info("[execute] 申请ID锛歿}锛屽鎵圭粨鏋滐細{}锛屽鎵逛汉锛歿}", applicationId, approved, approverId);
                
                // 执行瀹℃壒閫昏緫
                visitorApplicationService.approveVisitorApplication(applicationId, approved, approvalReason);
                
                // 鑾峰彇申请信息
                VisitorApplicationDO application = visitorApplicationService.getVisitorApplication(applicationId);
                
                if(approved) {
                    // 瀹℃壒通过锛屽彂閫侀€氱煡
                    visitorNotificationService.sendApplicationApprovedNotification(application);
                    
                    // 设置娴佺▼鍙橀噺锛岀敤浜庡悗缁妭鐐瑰垽鏂?                    execution.setVariable("needTraining", application.getTrainingCompleted() == 0);
                    execution.setVariable("needQrCode", true);
                } else {
                    // 瀹℃壒驳回锛屽彂閫侀€氱煡
                    visitorNotificationService.sendApplicationRejectedNotification(application, approvalReason);
                }

                // 记录瀹℃壒结果列版祦绋嬪彉閲?                execution.setVariable("approvalCompleted", true);
                execution.setVariable("approvalTime", java.time.LocalDateTime.now().toString());
            }
            
            log.info("[execute] 访客申请瀹℃壒濮旀墭执行完成");
            
        } catch(Exception e) {
            log.error("[execute] 访客申请瀹℃壒濮旀墭执行失败", e);
            throw new RuntimeException("瀹℃壒濮旀墭执行失败", e);
        }
    }

}

