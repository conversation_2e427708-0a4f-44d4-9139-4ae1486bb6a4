package cn.iocoder.yudao.module.visitor.service.training;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.visitor.controller.admin.training.vo.VisitorTrainingRecordCreateReqVO;
import cn.iocoder.yudao.module.visitor.controller.admin.training.vo.VisitorTrainingRecordPageReqVO;
import cn.iocoder.yudao.module.visitor.controller.admin.training.vo.VisitorTrainingRecordUpdateReqVO;
import cn.iocoder.yudao.module.visitor.dal.dataobject.VisitorTrainingDO;
import cn.iocoder.yudao.module.visitor.dal.dataobject.VisitorTrainingRecordDO;
import cn.iocoder.yudao.module.visitor.dal.mysql.VisitorTrainingMapper;
import cn.iocoder.yudao.module.visitor.dal.mysql.VisitorTrainingRecordMapper;
import cn.iocoder.yudao.module.visitor.enums.TrainingStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.visitor.enums.ErrorCodeConstants.*;

/**
 * 璁垮鍩硅瀹屾垚记录 Service 瀹炵幇绫? *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Service
@Validated
@Slf4j
public class VisitorTrainingRecordServiceImpl implements VisitorTrainingRecordService {

    @Resource
    private VisitorTrainingRecordMapper visitorTrainingRecordMapper;

    @Resource
    private VisitorTrainingMapper visitorTrainingMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createVisitorTrainingRecord(@Valid VisitorTrainingRecordCreateReqVO createReqVO) {
        log.info("[createVisitorTrainingRecord] 创建璁垮鍩硅记录锛屽弬鏁帮細{}", createReqVO);
        
        // 鎻掑叆鍩硅记录
        VisitorTrainingRecordDO trainingRecord = BeanUtils.toBean(createReqVO, VisitorTrainingRecordDO.class);
        visitorTrainingRecordMapper.insert(trainingRecord);
        
        log.info("[createVisitorTrainingRecord] 璁垮鍩硅记录创建鎴愬姛锛孖D锛歿}", trainingRecord.getId());
        
        return trainingRecord.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateVisitorTrainingRecord(@Valid VisitorTrainingRecordUpdateReqVO updateReqVO) {
        log.info("[updateVisitorTrainingRecord] 更新璁垮鍩硅记录锛屽弬鏁帮細{}", updateReqVO);
        
        // 鏍￠獙瀛樺湪
        validateVisitorTrainingRecordExists(updateReqVO.getId());
        
        // 更新鍩硅记录
        VisitorTrainingRecordDO updateObj = BeanUtils.toBean(updateReqVO, VisitorTrainingRecordDO.class);
        visitorTrainingRecordMapper.updateById(updateObj);
        
        log.info("[updateVisitorTrainingRecord] 璁垮鍩硅记录更新鎴愬姛锛孖D锛歿}", updateReqVO.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteVisitorTrainingRecord(Long id) {
        log.info("[deleteVisitorTrainingRecord] 删除璁垮鍩硅记录锛孖D锛歿}", id);
        
        // 鏍￠獙瀛樺湪
        validateVisitorTrainingRecordExists(id);
        
        // 删除
        visitorTrainingRecordMapper.deleteById(id);
        
        log.info("[deleteVisitorTrainingRecord] 璁垮鍩硅记录删除鎴愬姛锛孖D锛歿}", id);
    }

    private VisitorTrainingRecordDO validateVisitorTrainingRecordExists(Long id) {
        VisitorTrainingRecordDO record = visitorTrainingRecordMapper.selectById(id);
        if (record == null) {
            throw exception(VISITOR_TRAINING_RECORD_NOT_EXISTS);
        }
        return record;
    }

    @Override
    public VisitorTrainingRecordDO getVisitorTrainingRecord(Long id) {
        return visitorTrainingRecordMapper.selectById(id);
    }

    @Override
    public List<VisitorTrainingRecordDO> getVisitorTrainingRecordList(Collection<Long> ids) {
        return visitorTrainingRecordMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<VisitorTrainingRecordDO> getVisitorTrainingRecordPage(VisitorTrainingRecordPageReqVO pageReqVO) {
        return visitorTrainingRecordMapper.selectPage(pageReqVO);
    }

    @Override
    public List<VisitorTrainingRecordDO> getVisitorTrainingRecordList(VisitorTrainingRecordPageReqVO exportReqVO) {
        return visitorTrainingRecordMapper.selectList(exportReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long startTraining(Long applicationId, Long trainingId, String visitorName) {
        log.info("[startTraining] 寮€濮嬪煿璁紝鐢宠ID锛歿}锛屽煿璁璉D锛歿}锛岃瀹細{}", applicationId, trainingId, visitorName);
        
        // 妫€鏌ユ槸鍚﹀凡瀛樺湪鍩硅记录
        VisitorTrainingRecordDO existingRecord = visitorTrainingRecordMapper
                .selectByApplicationIdAndTrainingId(applicationId, trainingId);
        if (existingRecord != null) {
            log.warn("[startTraining] 鍩硅记录宸插瓨鍦紝记录ID锛歿}", existingRecord.getId());
            return existingRecord.getId();
        }
        
        // 创建鍩硅记录
        VisitorTrainingRecordDO record = new VisitorTrainingRecordDO();
        record.setApplicationId(applicationId);
        record.setTrainingId(trainingId);
        record.setVisitorName(visitorName);
        record.setStartTime(LocalDateTime.now());
        record.setCompletionStatus(TrainingStatusEnum.NOT_STARTED.getStatus());
        
        visitorTrainingRecordMapper.insert(record);
        
        log.info("[startTraining] 鍩硅寮€濮嬭褰曟垚鍔燂紝记录ID锛歿}", record.getId());
        
        return record.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void completeTraining(Long recordId, Integer examScore, Object examAnswers, String signatureImage) {
        log.info("[completeTraining] 瀹屾垚鍩硅锛岃褰旾D锛歿}锛岃€冭瘯鍒嗘暟锛歿}", recordId, examScore);
        
        // 鏍￠獙记录瀛樺湪
        VisitorTrainingRecordDO record = validateVisitorTrainingRecordExists(recordId);
        
        // 鏍￠獙鏄惁宸插畬鎴?        if (TrainingStatusEnum.COMPLETED.getStatus().equals(record.getCompletionStatus())) {
            throw exception(VISITOR_TRAINING_RECORD_ALREADY_COMPLETED);
        }
        
        // 鑾峰彇鍩硅閰嶇疆
        VisitorTrainingDO training = visitorTrainingMapper.selectById(record.getTrainingId());
        if (training == null) {
            throw exception(VISITOR_TRAINING_NOT_EXISTS);
        }
        
        // 妫€查询€冭瘯鍒嗘暟
        if (training.getHasExam().equals(1) && examScore != null) {
            if (examScore < training.getPassScore()) {
                throw exception(VISITOR_TRAINING_EXAM_SCORE_TOO_LOW);
            }
        }
        
        // 璁＄畻鍩硅鏃堕暱
        int duration = (int) java.time.Duration.between(record.getStartTime(), LocalDateTime.now()).toMinutes();
        
        // 妫€鏌ュ煿璁椂闂?        
        if (training.getMinDuration() != null && duration < training.getMinDuration()) {
            throw exception(VISITOR_TRAINING_DURATION_TOO_SHORT);
        }
        
        // 更新鍩硅记录
        VisitorTrainingRecordDO updateObj = new VisitorTrainingRecordDO();
        updateObj.setId(recordId);
        updateObj.setEndTime(LocalDateTime.now());
        updateObj.setDuration(duration);
        updateObj.setCompletionStatus(TrainingStatusEnum.COMPLETED.getStatus());
        updateObj.setExamScore(examScore);
        updateObj.setSignatureImage(signatureImage);
        
        visitorTrainingRecordMapper.updateById(updateObj);
        
        log.info("[completeTraining] 鍩硅瀹屾垚记录鎴愬姛锛岃褰旾D锛歿}锛屾椂闀匡細{}鍒嗛挓", recordId, duration);
    }

    @Override
    public boolean isAllRequiredTrainingCompleted(Long applicationId, Integer visitorType) {
        log.info("[isAllRequiredTrainingCompleted] 妫€鏌ュ繀淇煿璁畬鎴愭儏鍐碉紝鐢宠ID锛歿}锛岃瀹㈢被鍨嬶細{}", applicationId, visitorType);
        
        // 鑾峰彇蹇呬慨鍩硅鍒楄〃
        List<VisitorTrainingDO> requiredTrainings = visitorTrainingMapper.selectRequiredByVisitorType(visitorType);
        if (requiredTrainings.isEmpty()) {
            return true; // 娌℃湁蹇呬慨鍩硅
        }
        
        // 鑾峰彇宸插畬鎴愮殑鍩硅记录
        List<VisitorTrainingRecordDO> completedRecords = visitorTrainingRecordMapper
                .selectCompletedByApplicationId(applicationId);
        
        // 妫€鏌ユ槸鍚︽墍鏈夊繀淇煿璁兘宸插畬鎴?        
        for (VisitorTrainingDO requiredTraining : requiredTrainings) {
            boolean completed = completedRecords.stream()
                    .anyMatch(record -> record.getTrainingId().equals(requiredTraining.getId()));
            if (!completed) {
                log.info("[isAllRequiredTrainingCompleted] 蹇呬慨鍩硅鏈畬鎴愶紝鍩硅ID锛歿}", requiredTraining.getId());
                return false;
            }
        }
        
        log.info("[isAllRequiredTrainingCompleted] 鎵€鏈夊繀淇煿璁凡瀹屾垚锛岀敵璇稩D锛歿}", applicationId);
        return true;
    }

    @Override
    public Object getTrainingProgress(Long applicationId) {
        log.info("[getTrainingProgress] 鑾峰彇鍩硅杩涘害锛岀敵璇稩D锛歿}", applicationId);
        
        // 鑾峰彇鍩硅记录
        List<VisitorTrainingRecordDO> records = visitorTrainingRecordMapper.selectListByApplicationId(applicationId);
        
        // 缁熻杩涘害
        long totalCount = records.size();
        long completedCount = records.stream()
                .mapToLong(record -> TrainingStatusEnum.COMPLETED.getStatus().equals(record.getCompletionStatus()) ? 1 : 0)
                .sum();
        
        // 杩斿洖杩涘害淇℃伅
        return new Object() {
            public final Long totalTrainings = totalCount;
            public final Long completedTrainings = completedCount;
            public final Double progressPercentage = totalCount > 0 ? (double) completedCount / totalCount * 100 : 0.0;
            public final Boolean allCompleted = totalCount > 0 && completedCount == totalCount;
        };
    }

}
