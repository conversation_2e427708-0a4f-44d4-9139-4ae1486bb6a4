package cn.iocoder.yudao.module.visitor.dal.mysql;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.visitor.controller.admin.training.vo.VisitorTrainingPageReqVO;
import cn.iocoder.yudao.module.visitor.dal.dataobject.VisitorTrainingDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collection;
import java.util.List;

/**
 * 访客培训配置 Mapper
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Mapper
public interface VisitorTrainingMapper extends BaseMapperX<VisitorTrainingDO> {

    default PageResult<VisitorTrainingDO> selectPage(VisitorTrainingPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<VisitorTrainingDO>()
                .likeIfPresent(VisitorTrainingDO::getTrainingName, reqVO.getTrainingName())
                .eqIfPresent(VisitorTrainingDO::getTrainingType, reqVO.getTrainingType())
                .eqIfPresent(VisitorTrainingDO::getVisitorType, reqVO.getVisitorType())
                .eqIfPresent(VisitorTrainingDO::getIsRequired, reqVO.getIsRequired())
                .eqIfPresent(VisitorTrainingDO::getHasExam, reqVO.getHasExam())
                .eqIfPresent(VisitorTrainingDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(VisitorTrainingDO::getCreateTime, reqVO.getCreateTime())
                .orderByAsc(VisitorTrainingDO::getSortOrder)
                .orderByDesc(VisitorTrainingDO::getId));
    }

    default List<VisitorTrainingDO> selectList(VisitorTrainingPageReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<VisitorTrainingDO>()
                .likeIfPresent(VisitorTrainingDO::getTrainingName, reqVO.getTrainingName())
                .eqIfPresent(VisitorTrainingDO::getTrainingType, reqVO.getTrainingType())
                .eqIfPresent(VisitorTrainingDO::getVisitorType, reqVO.getVisitorType())
                .eqIfPresent(VisitorTrainingDO::getIsRequired, reqVO.getIsRequired())
                .eqIfPresent(VisitorTrainingDO::getHasExam, reqVO.getHasExam())
                .eqIfPresent(VisitorTrainingDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(VisitorTrainingDO::getCreateTime, reqVO.getCreateTime())
                .orderByAsc(VisitorTrainingDO::getSortOrder)
                .orderByDesc(VisitorTrainingDO::getId));
    }

    default VisitorTrainingDO selectByTrainingName(String trainingName) {
        return selectOne(VisitorTrainingDO::getTrainingName, trainingName);
    }

    default List<VisitorTrainingDO> selectListByStatus(Integer status) {
        return selectList(VisitorTrainingDO::getStatus, status);
    }

    default List<VisitorTrainingDO> selectListByVisitorType(Integer visitorType) {
        return selectList(new LambdaQueryWrapperX<VisitorTrainingDO>()
                .eq(VisitorTrainingDO::getStatus, 1) // 鍚敤状态?
                // .and(wrapper -> wrapper
                        .eq(VisitorTrainingDO::getVisitorType, visitorType)
                        .or()
                        .eq(VisitorTrainingDO::getVisitorType, 0)) // 鍏ㄩ儴类型
                .orderByAsc(VisitorTrainingDO::getSortOrder));
    }

    default List<VisitorTrainingDO> selectRequiredByVisitorType(Integer visitorType) {
        return selectList(new LambdaQueryWrapperX<VisitorTrainingDO>()
                .eq(VisitorTrainingDO::getStatus, 1) // 鍚敤状态?                .eq(VisitorTrainingDO::getIsRequired, 1) // 必修
                .and(wrapper -> wrapper
                        .eq(VisitorTrainingDO::getVisitorType, visitorType)
                        .or()
                        .eq(VisitorTrainingDO::getVisitorType, 0)) // 鍏ㄩ儴类型
                .orderByAsc(VisitorTrainingDO::getSortOrder));
    }

    default List<VisitorTrainingDO> selectListByTrainingType(Integer trainingType) {
        return selectList(new LambdaQueryWrapperX<VisitorTrainingDO>()
                .eq(VisitorTrainingDO::getTrainingType, trainingType)
                .eq(VisitorTrainingDO::getStatus, 1)
                .orderByAsc(VisitorTrainingDO::getSortOrder));
    }

    default List<VisitorTrainingDO> selectListByIds(Collection<Long> ids) {
        return selectList(new LambdaQueryWrapperX<VisitorTrainingDO>()
                .inIfPresent(VisitorTrainingDO::getId, ids)
                .orderByAsc(VisitorTrainingDO::getSortOrder));
    }

    default List<VisitorTrainingDO> selectExamTrainings() {
        return selectList(new LambdaQueryWrapperX<VisitorTrainingDO>()
                .eq(VisitorTrainingDO::getHasExam, 1)
                .eq(VisitorTrainingDO::getStatus, 1)
                .orderByAsc(VisitorTrainingDO::getSortOrder));
    }

    /**
     * 统计培训配置鏁伴噺鎸夊煿璁被鍨嬪垎缁?     */
    List<Object[]> selectCountGroupByTrainingType();

    /**
     * 统计培训配置鏁伴噺鎸夎瀹㈢被鍨嬪垎缁?     */
    List<Object[]> selectCountGroupByVisitorType();

}

