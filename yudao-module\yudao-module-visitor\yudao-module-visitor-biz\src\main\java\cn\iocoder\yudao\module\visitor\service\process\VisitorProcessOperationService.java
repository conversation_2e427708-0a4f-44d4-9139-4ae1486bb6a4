package cn.iocoder.yudao.module.visitor.service.process;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.visitor.dal.dataobject.VisitorProcessOperationDO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 访客娴佺▼操作员记录 Service 接口
 *
 * <AUTHOR>
 * @since 2025-01-12
 */
public interface VisitorProcessOperationService {

    /**
     * 创建娴佺▼操作员记录
     *
     * @param operation 操作员记录
     * @return 编
     */
    Long createProcessOperation(VisitorProcessOperationDO operation);

    /**
     * 记录娴佺▼操作员
     *
     * @param applicationId 申请ID
     * @param processInstanceId 娴佺▼实例ID
     * @param taskId 任务ID
     * @param operationType 操作员类型
     * @param operationResult 操作员结果
     * @param operatorId 操作员浜篒D
     * @param operatorName 操作员浜哄鍚?     * @param operationContent 操作员鍐呭
     * @param operationReason 操作员鍘熷洜
     * @return 操作员记录ID
     */
    Long recordOperation(Long applicationId, String processInstanceId, String taskId, 
                        Integer operationType, Integer operationResult, Long operatorId, 
                        String operatorName, String operationContent, String operationReason);

    /**
     * 获得娴佺▼操作员记录
     *
     * @param id 编
     * @return 娴佺▼操作员记录
     */
    VisitorProcessOperationDO getProcessOperation(Long id);

    /**
     * 树规嵁申请ID鑾峰彇操作员记录列表
     *
     * @param applicationId 申请ID
     * @return 操作员记录列表
     */
    List<VisitorProcessOperationDO> getOperationsByApplicationId(Long applicationId);

    /**
     * 树规嵁娴佺▼实例ID鑾峰彇操作员记录列表
     *
     * @param processInstanceId 娴佺▼实例ID
     * @return 操作员记录列表
     */
    List<VisitorProcessOperationDO> getOperationsByProcessInstanceId(String processInstanceId);

    /**
     * 树规嵁操作员类型鑾峰彇操作员记录列表
     *
     * @param operationType 操作员类型
     * @param startTime 开始时间     * @param endTime 结束时间
     * @return 操作员记录列表
     */
    List<VisitorProcessOperationDO> getOperationsByType(Integer operationType, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 鑾峰彇操作员统计
     *
     * @param startTime 开始时间     * @param endTime 结束时间
     * @return 统计结果
     */
    Object getOperationStatistics(LocalDateTime startTime, LocalDateTime endTime);

}

