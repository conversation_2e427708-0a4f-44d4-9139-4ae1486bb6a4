<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao-module</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>yudao-module-visitor</artifactId>
    <packaging>pom</packaging>

    <name>${project.artifactId}</name>
    <description>访客管理模块，包含访客申请、审批流程、培训管理、进出记录等功能</description>
    <url>https://github.com/YunaiV/ruoyi-vue-pro</url>

    <modules>
        <module>yudao-module-visitor-api</module>
        <module>yudao-module-visitor-biz</module>
    </modules>

</project>

