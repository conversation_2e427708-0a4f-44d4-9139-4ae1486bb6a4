package cn.iocoder.yudao.module.visitor.controller.app.application;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import cn.iocoder.yudao.module.visitor.controller.admin.application.vo.VisitorApplicationCreateReqVO;
import cn.iocoder.yudao.module.visitor.controller.admin.application.vo.VisitorApplicationPageReqVO;
import cn.iocoder.yudao.module.visitor.controller.admin.application.vo.VisitorApplicationRespVO;
import cn.iocoder.yudao.module.visitor.convert.application.VisitorApplicationConvert;
import cn.iocoder.yudao.module.visitor.dal.dataobject.VisitorApplicationDO;
import cn.iocoder.yudao.module.visitor.service.application.VisitorApplicationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * 用户 APP - 访客申请 Controller
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Tag(name = "用户 APP - 访客申请")
@RestController
@RequestMapping("/visitor/application")
@Validated
@Slf4j
public class AppVisitorApplicationController {

    @Resource
    private VisitorApplicationService visitorApplicationService;

    @PostMapping("/create")
    @Operation(summary = "创建访客申请")
    @PreAuthorize("hasRole('ROLE_USER')")
    public CommonResult<Long> createVisitorApplication(@Valid @RequestBody VisitorApplicationCreateReqVO createReqVO) {
        return success(visitorApplicationService.createVisitorApplication(createReqVO));
    }

    @GetMapping("/get")
    @Operation(summary = "获得访客申请")
    @Parameter(name = "id", description = "编", required = true, example = "1024")
    @PreAuthorize("hasRole('ROLE_USER')")
    public CommonResult<VisitorApplicationRespVO> getVisitorApplication(@RequestParam("id") Long id) {
        VisitorApplicationDO visitorApplication = visitorApplicationService.getVisitorApplication(id);
        return success(BeanUtils.toBean(visitorApplication, VisitorApplicationRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得鎴戠殑访客申请分页")
    @PreAuthorize("hasRole('ROLE_USER')")
    public CommonResult<PageResult<VisitorApplicationRespVO>> getMyVisitorApplicationPage(@Valid VisitorApplicationPageReqVO pageReqVO) {
        // 鍙煡璇㈠綋鍓嶇敤鎴风殑申请
        PageResult<VisitorApplicationDO> pageResult = visitorApplicationService.getVisitorApplicationPage(pageReqVO);
        return success(VisitorApplicationConvert.INSTANCE.convertPage(pageResult));
    }

    @PostMapping("/submit")
    @Operation(summary = "鎻愪氦访客申请")
    @Parameter(name = "id", description = "申请ID", required = true)
    @PreAuthorize("hasRole('ROLE_USER')")
    public CommonResult<String> submitVisitorApplication(@RequestParam("id") Long id) {
        String processInstanceId = visitorApplicationService.submitVisitorApplication(id);
        return success(processInstanceId);
    }

    @GetMapping("/qr-code")
    @Operation(summary = "鑾峰彇访客二维码?)
    @Parameter(name = "id", description = "申请ID", required = true)
    @PreAuthorize("hasRole('ROLE_USER')")
    public CommonResult<String> getVisitorQrCode(@RequestParam("id") Long id) {
        VisitorApplicationDO application = visitorApplicationService.getVisitorApplication(id);
        if(application == null) {
            return success(null);
        }
        return success(application.getQrCode());
    }

    @GetMapping("/training-progress")
    @Operation(summary = "鑾峰彇培训进度")
    @Parameter(name = "id", description = "申请ID", required = true)
    @PreAuthorize("hasRole('ROLE_USER')")
    public CommonResult<Object> getTrainingProgress(@RequestParam("id") Long id) {
        // 杩欓噷库旇璋冪敤培训服务鑾峰彇进度
        // Object progress = visitorTrainingRecordService.getTrainingProgress(id);
        return success(new Object());
    }

}

