package cn.iocoder.yudao.module.visitor.convert.record;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.visitor.controller.admin.record.vo.VisitorRecordCreateReqVO;
import cn.iocoder.yudao.module.visitor.controller.admin.record.vo.VisitorRecordRespVO;
import cn.iocoder.yudao.module.visitor.controller.admin.record.vo.VisitorRecordUpdateReqVO;
import cn.iocoder.yudao.module.visitor.dal.dataobject.VisitorRecordDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 访客进出记录 Convert
 *
 * <AUTHOR>
 * @since 2025-01-12
 */
@Mapper
public interface VisitorRecordConvert {

    VisitorRecordConvert INSTANCE = Mappers.getMapper(VisitorRecordConvert.class);

    VisitorRecordDO convert(VisitorRecordCreateReqVO bean);

    VisitorRecordDO convert(VisitorRecordUpdateReqVO bean);

    VisitorRecordRespVO convert(VisitorRecordDO bean);

    List<VisitorRecordRespVO> convertList(List<VisitorRecordDO> list);

    PageResult<VisitorRecordRespVO> convertPage(PageResult<VisitorRecordDO> page);

}

