package cn.iocoder.yudao.module.visitor.controller.admin.training.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 管理后台 - 访客培训完成记录分页 Request VO
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Schema(description = "管理后台 - 访客培训完成记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class VisitorTrainingRecordPageReqVO extends PageParam {

    @Schema(description = "申请单ID", example = "1024")
    private Long applicationId;

    @Schema(description = "培训ID", example = "1")
    private Long trainingId;

    @Schema(description = "访客姓名", example = "张三")
    private String visitorName;

    @Schema(description = "完成状态?, example = "1")
    private Integer completionStatus;

    @Schema(description = "最小浣庤€冭瘯分数", example = "60")
    private Integer minExamScore;

    @Schema(description = "最小楂樿€冭瘯分数", example = "100")
    private Integer maxExamScore;

    @Schema(description = "开始嬫椂闂?)
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] startTime;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}

