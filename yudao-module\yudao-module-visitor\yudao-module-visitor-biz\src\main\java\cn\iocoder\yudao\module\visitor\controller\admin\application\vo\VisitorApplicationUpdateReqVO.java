package cn.iocoder.yudao.module.visitor.controller.admin.application.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import jakarta.validation.constraints.NotNull;

/**
 * 管理后台 - 访客申请更新 Request VO
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Schema(description = "管理后台 - 访客申请更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class VisitorApplicationUpdateReqVO extends VisitorApplicationCreateReqVO {

    @Schema(description = "编", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "编不能为空")
    private Long id;

}
