#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Java语法错误修复工具
专门修复编码修复后剩余的语法问题
"""

import os
import re
from pathlib import Path
import shutil

class JavaSyntaxFixer:
    def __init__(self, root_path: str):
        self.root_path = Path(root_path)
        self.fixed_files = []
        self.stats = {
            'total_files': 0,
            'fixed_files': 0,
            'syntax_fixes': 0
        }

        # 更完整的乱码映射表
        self.remaining_garbled = {
            '嬫椂闂': '时间',
            '缁撴潫': '结束',
            '鐢ㄤ簬': '用于',
            '鏁版嵁搴': '数据库',
            '涓婚敭鑷': '主键自增',
            '绛夋暟鎹簱': '等数据库',
            '鍙互涓嶅啓': '可以不写',
            '翠笉鑳戒负绌': '不能为空',
            '浜哄鍚': '人姓名',
            '浜虹被鍨': '人类型',
            '鑷畾涔夋槧灏勬柟娉': '自定义映射方法',
            '鍙傛暟': '参数',
            '杩斿洖': '返回',
            '鍊': '值',
            '鏂规硶': '方法',
            '绫诲瀷': '类型',
            '瀵硅薄': '对象',
            '灞炴': '属性',
            '瀛楁': '字段',
            '琛': '表',
            '鍒': '列',
            '涓婚敭': '主键',
            '澶栭敭': '外键',
            '绱㈠紩': '索引',
            '鍞竴': '唯一',
            '闈炵┖': '非空',
            '榛樿': '默认',
            '鍒涘缓': '创建',
            '鏇存柊': '更新',
            '鍒犻櫎': '删除',
            '鏌ヨ': '查询',
            '鎻掑叆': '插入',
            '淇敼': '修改',
            '鎿嶄綔': '操作',
            '缁撴灉': '结果',
            '鐘舵': '状态',
            '绫诲瀷': '类型',
            '鍒嗙被': '分类',
            '鍒嗙粍': '分组',
            '鎺掑簭': '排序',
            '绛涢': '筛选',
            '杩囨护': '过滤',
            '鎼滅储': '搜索',
            '鍖归厤': '匹配',
            '姣旇緝': '比较',
            '鍒ゆ柇': '判断',
            '楠岃瘉': '验证',
            '妫鏌': '检查',
            '纭': '确认',
            '鍙栨秷': '取消',
            '椹冲洖': '驳回',
            '閫氳繃': '通过',
            '澶辫触': '失败',
            '鎴愬姛': '成功',
            '瀹屾垚': '完成',
            '寮濮': '开始',
            '缁撴潫': '结束',
            '鏆傚仠': '暂停',
            '缁х画': '继续',
            '閲嶅': '重新',
            '閲嶈瘯': '重试',
            '閲嶇疆': '重置',
            '鍒濆鍖': '初始化',
            '閰嶇疆': '配置',
            '璁剧疆': '设置',
            '鍙傛暟': '参数',
            '閫夐」': '选项',
            '灞炴': '属性',
            '鐗规': '特性',
            '鍔熻兘': '功能',
            '妯″潡': '模块',
            '缁勪欢': '组件',
            '鏈嶅姟': '服务',
            '鎺ュ彛': '接口',
            '瀹炵幇': '实现',
            '缁ф壙': '继承',
            '灏佽': '封装',
            '澶氭': '多态',
            '鎶借薄': '抽象',
            '鍏蜂綋': '具体',
            '閫氱敤': '通用',
            '涓撶敤': '专用',
            '鍏叡': '公共',
            '绉佹湁': '私有',
            '淇濇姢': '保护',
            '榛樿': '默认',
            '闈欐': '静态',
            '鍔ㄦ': '动态',
            '鍚屾': '同步',
            '寮傛': '异步',
            '骞跺彂': '并发',
            '绾跨▼': '线程',
            '杩涚▼': '进程',
            '浠诲姟': '任务',
            '璋冨害': '调度',
            '鎵ц': '执行',
            '杩愯': '运行',
            '鍋滄': '停止',
            '鍚姩': '启动',
            '鍏抽棴': '关闭',
            '鎵撳紑': '打开',
            '杩炴帴': '连接',
            '鏂紑': '断开',
            '缁戝畾': '绑定',
            '瑙ｇ粦': '解绑',
            '娉ㄥ唽': '注册',
            '娉ㄩ攢': '注销',
            '鐧诲綍': '登录',
            '鐧诲嚭': '登出',
            '鎺堟潈': '授权',
            '璁よ瘉': '认证',
            '楠岃瘉': '验证',
            '鍔犲瘑': '加密',
            '瑙ｅ瘑': '解密',
            '绛惧悕': '签名',
            '楠岀': '验签',
            '鍘嬬缉': '压缩',
            '瑙ｅ帇': '解压',
            '缂栫爜': '编码',
            '瑙ｇ爜': '解码',
            '搴忓垪鍖': '序列化',
            '鍙嶅簭鍒楀寲': '反序列化',
            '杞崲': '转换',
            '鏄犲皠': '映射',
            '缈昏瘧': '翻译',
            '瑙ｆ瀽': '解析',
            '鏋勫缓': '构建',
            '鐢熸垚': '生成',
            '鍒涘缓': '创建',
            '鍒犻櫎': '删除',
            '淇敼': '修改',
            '鏇存柊': '更新',
            '鏌ヨ': '查询',
            '鎼滅储': '搜索',
            '缁熻': '统计',
            '鍒嗘瀽': '分析',
            '璁＄畻': '计算',
            '澶勭悊': '处理',
            '鎿嶄綔': '操作',
            '绠＄悊': '管理',
            '鎺у埗': '控制',
            '鐩戞帶': '监控',
            '璋冭瘯': '调试',
            '娴嬭瘯': '测试',
            '楠岃瘉': '验证',
            '妫娴': '检测',
            '鎵弿': '扫描',
            '鍒嗘瀽': '分析',
            '璇婃柇': '诊断',
            '淇': '修复',
            '浼樺寲': '优化',
            '鎻愬崌': '提升',
            '鏀硅繘': '改进',
            '澧炲己': '增强',
            '鎵╁睍': '扩展',
            '鍗囩骇': '升级',
            '闄嶇骇': '降级',
            '鍥炴粴': '回滚',
            '澶囦唤': '备份',
            '鎭㈠': '恢复',
            '杩樺師': '还原',
            '閲嶇疆': '重置',
            '娓呯悊': '清理',
            '鍒犻櫎': '删除',
            '閿姣': '销毁',
            '閲婃斁': '释放',
            '鍥炴敹': '回收',
            '鍨冨溇': '垃圾',
            '鍐呭瓨': '内存',
            '瀛樺偍': '存储',
            '纾佺洏': '磁盘',
            '缃戠粶': '网络',
            '鏁版嵁搴': '数据库',
            '缂撳瓨': '缓存',
            '闃熷垪': '队列',
            '鏍': '栈',
            '鍫': '堆',
            '閾捐〃': '链表',
            '鏁扮粍': '数组',
            '闆嗗悎': '集合',
            '鍒楄〃': '列表',
            '鏄犲皠': '映射',
            '瀛楀吀': '字典',
            '鍝堝笇': '哈希',
            '鏍': '树',
            '鍥': '图',
            '绠楁硶': '算法',
            '鏁版嵁缁撴瀯': '数据结构',
            '璁捐妯″紡': '设计模式',
            '鏋舵瀯': '架构',
            '妗嗘灦': '框架',
            '搴': '库',
            '宸ュ叿': '工具',
            '缁勪欢': '组件',
            '鎻掍欢': '插件',
            '妯″潡': '模块',
            '鍖': '包',
            '绫': '类',
            '瀵硅薄': '对象',
            '瀹炰緥': '实例',
            '鏂规硶': '方法',
            '鍑芥暟': '函数',
            '杩囩▼': '过程',
            '浜嬩欢': '事件',
            '娑堟伅': '消息',
            '淇″彿': '信号',
            '閫氱煡': '通知',
            '鎻愰啋': '提醒',
            '璀﹀憡': '警告',
            '閿欒': '错误',
            '寮傚父': '异常',
            '鏁呴殰': '故障',
            '闂': '问题',
            '瑙ｅ喅': '解决',
            '澶勭悊': '处理',
            '淇': '修复',
            '瑙ｅ喅鏂规': '解决方案',
            '澶勭悊鏂规': '处理方案',
            '淇鏂规': '修复方案',
            '瀹炴柦鏂规': '实施方案',
            '鎵ц鏂规': '执行方案',
            '娴嬭瘯鏂规': '测试方案',
            '楠岃瘉鏂规': '验证方案',
            '閮ㄧ讲鏂规': '部署方案',
            '涓婄嚎鏂规': '上线方案',
            '涓嬬嚎鏂规': '下线方案',
            '鍥炴粴鏂规': '回滚方案',
            '搴旀ユ柟妗': '应急方案',
            '澶囦唤鏂规': '备份方案',
            '鎭㈠鏂规': '恢复方案',
            '瀹夊叏鏂规': '安全方案',
            '鎬ц兘鏂规': '性能方案',
            '浼樺寲鏂规': '优化方案',
            '鎵╁睍鏂规': '扩展方案',
            '鍗囩骇鏂规': '升级方案',
            '杩佺Щ鏂规': '迁移方案',
            '闆嗘垚鏂规': '集成方案',
            '瀵规帴鏂规': '对接方案',
            '鍚屾鏂规': '同步方案',
            '寮傛鏂规': '异步方案',
            '骞跺彂鏂规': '并发方案',
            '鍒嗗竷寮忔柟妗': '分布式方案',
            '寰湇鍔℃柟妗': '微服务方案',
            '浜戝師鐢熸柟妗': '云原生方案',
            '瀹瑰櫒鍖栨柟妗': '容器化方案',
            '鑷姩鍖栨柟妗': '自动化方案',
            '鏅鸿兘鍖栨柟妗': '智能化方案',
            '鏁板瓧鍖栨柟妗': '数字化方案'
        }

    def fix_syntax_errors(self, content: str) -> tuple[str, int]:
        """修复语法错误"""
        fixes = 0
        original_content = content

        # 1. 修复剩余的中文乱码
        for garbled, correct in self.remaining_garbled.items():
            if garbled in content:
                content = content.replace(garbled, correct)
                fixes += 1

        # 2. 修复缺失的引号
        # 修复 @Schema 注解中缺失的引号
        content = re.sub(r'@Schema\(description = "([^"]*), requiredMode',
                        r'@Schema(description = "\1", requiredMode', content)

        # 修复 @NotNull 注解中缺失的引号
        content = re.sub(r'@NotNull\(message = "([^"]*)\)',
                        r'@NotNull(message = "\1")', content)

        # 3. 修复缺失的括号
        # 修复方法调用中缺失的括号
        content = re.sub(r'(\w+)\s*\(\s*([^)]*)\s*\)',
                        r'\1(\2)', content)

        # 4. 修复非法字符
        illegal_chars = ['\ue5e7', '\ue044', '\ue583', '\ue179']
        for char in illegal_chars:
            if char in content:
                content = content.replace(char, '')
                fixes += 1

        # 5. 修复注释中的乱码
        content = re.sub(r'// ([^/\n]*)', lambda m: f'// {self.fix_comment_text(m.group(1))}', content)
        content = re.sub(r'/\*\*([^*]*)\*/', lambda m: f'/**{self.fix_comment_text(m.group(1))}*/', content, flags=re.DOTALL)

        # 6. 修复字符串中的乱码
        content = re.sub(r'"([^"]*)"', lambda m: f'"{self.fix_string_text(m.group(1))}"', content)

        if content != original_content:
            fixes += 1

        return content, fixes

    def fix_comment_text(self, text: str) -> str:
        """修复注释文本"""
        for garbled, correct in self.remaining_garbled.items():
            text = text.replace(garbled, correct)
        return text

    def fix_string_text(self, text: str) -> str:
        """修复字符串文本"""
        for garbled, correct in self.remaining_garbled.items():
            text = text.replace(garbled, correct)
        return text

    def fix_java_file(self, file_path: Path) -> bool:
        """修复单个Java文件的语法错误"""
        try:
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 修复语法错误
            fixed_content, fixes = self.fix_syntax_errors(content)

            if fixes > 0:
                # 重新保存文件
                with open(file_path, 'w', encoding='utf-8', newline='') as f:
                    f.write(fixed_content)

                self.stats['syntax_fixes'] += fixes
                self.fixed_files.append(str(file_path))
                print(f"语法修复完成: {file_path.name} ({fixes}个修复)")
                return True

            return True

        except Exception as e:
            print(f"语法修复失败: {file_path} - {e}")
            return False

    def scan_and_fix(self, target_dir: str = "yudao-module/yudao-module-visitor/yudao-module-visitor-biz"):
        """扫描并修复指定目录下的Java文件语法错误"""
        target_path = Path(self.root_path) / target_dir

        if not target_path.exists():
            print(f"目标目录不存在: {target_path}")
            return

        # 查找所有Java文件
        java_files = list(target_path.rglob("*.java"))
        self.stats['total_files'] = len(java_files)

        print(f"开始语法修复，找到 {len(java_files)} 个Java文件")

        # 修复每个文件
        for java_file in java_files:
            if self.fix_java_file(java_file):
                self.stats['fixed_files'] += 1

        # 输出统计信息
        self.print_summary()

    def print_summary(self):
        """输出修复总结"""
        print("\n" + "="*50)
        print("语法修复总结")
        print("="*50)
        print(f"总文件数: {self.stats['total_files']}")
        print(f"处理文件数: {self.stats['fixed_files']}")
        print(f"语法修复次数: {self.stats['syntax_fixes']}")

        if self.fixed_files:
            print(f"\n修复的文件:")
            for file in self.fixed_files[:10]:  # 只显示前10个
                print(f"  - {Path(file).name}")
            if len(self.fixed_files) > 10:
                print(f"  ... 还有 {len(self.fixed_files) - 10} 个文件")

if __name__ == "__main__":
    # 使用当前目录作为根目录
    fixer = JavaSyntaxFixer(".")
    fixer.scan_and_fix()