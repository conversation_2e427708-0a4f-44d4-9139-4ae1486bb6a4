package cn.iocoder.yudao.module.visitor.controller.app.guard.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 用户 APP - 访客入园登记 Request VO
 *
 * <AUTHOR>
 * @since 2025-01-12
 */
@Schema(description = "用户 APP - 访客入园登记 Request VO")
@Data
public class AppVisitorCheckInReqVO {

    @Schema(description = "申请ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "申请ID不能为空")
    private Long applicationId;

    @Schema(description = "门岗浣嶇疆", example = "涓滈棬")
    private String gateLocation;

    @Schema(description = "验证鏂瑰紡", example = "1")
    private Integer verificationMethod;

    @Schema(description = "车牌鍙?, example = "绮12345")
    private String vehiclePlate;

    @Schema(description = "车辆照片URL", example = "https://example.com/vehicle.jpg")
    private String vehiclePhoto;

    @Schema(description = "鐜板満访客照片URL", example = "https://example.com/visitor.jpg")
    private String visitorPhoto;

    @Schema(description = "浣撴俯锛堟憚姘忓害锛?, example = "36.5")
    private BigDecimal temperature;

    @Schema(description = "健康状态?, example = "1")
    private Integer healthStatus;

    @Schema(description = "瀹夋结果", example = "1")
    private Integer securityCheckResult;

    @Schema(description = "异常鎯呭喌描述", example = "鏃犲紓甯?)
    private String abnormalInfo;

    @Schema(description = "异常照片URL鏁扮粍")
    private List<String> abnormalPhotos;

    @Schema(description = "备注信息", example = "正常入园")
    private String remarks;

}

