#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Java文件编码修复工具
用于修复yudao-module-visitor-biz模块中的编码问题
"""

import os
import re
import chardet
from pathlib import Path
import shutil
from typing import Dict, List, Tuple, Optional

class JavaEncodingFixer:
    def __init__(self, root_path: str):
        self.root_path = Path(root_path)
        self.backup_dir = self.root_path / "backup_encoding_fix"
        self.fixed_files = []
        self.failed_files = []
        self.stats = {
            'total_files': 0,
            'fixed_files': 0,
            'failed_files': 0,
            'encoding_fixes': 0,
            'syntax_fixes': 0
        }
        
        # 中文乱码映射表
        self.garbled_mappings = {
            # 常见乱码模式
            '嬫椂闂?': '时间',
            '翠笉鑳戒负绌?': '不能为空',
            '缁撴潫': '结束',
            '开始': '开始',
            '浜哄鍚?': '人姓名',
            '浜虹被鍨?': '人类型',
            '鐢ㄤ簬': '用于',
            '鏁版嵁搴?': '数据库',
            '涓婚敭鑷': '主键自增',
            '绛夋暟鎹簱': '等数据库',
            '鍙互涓嶅啓': '可以不写',
            '访客': '访客',
            '记录': '记录',
            '培训': '培训',
            '申请': '申请',
            '审批': '审批',
            '二维码': '二维码',
            '扫描': '扫描',
            '验证': '验证',
            '安检': '安检',
            '通过': '通过',
            '未通过': '未通过',
            '入园': '入园',
            '出园': '出园',
            '登记': '登记',
            '流程': '流程',
            '操作': '操作',
            '结果': '结果',
            '驳回': '驳回',
            '取消': '取消',
            '退回修改': '退回修改',
            '操作员': '操作员',
            '系统': '系统',
            '管理员': '管理员',
            '用户': '用户',
            # 特殊字符修复
            '号': '',  # 移除错误的"号"字符
            '\ufeff': '',  # 移除BOM
            '\ue5e7': '',  # 移除非法Unicode字符
            '\ue044': '',
            '\ue583': '',
            '\ue179': '',
        }
    
    def create_backup(self):
        """创建备份目录"""
        if self.backup_dir.exists():
            shutil.rmtree(self.backup_dir)
        self.backup_dir.mkdir(parents=True)
        print(f"创建备份目录: {self.backup_dir}")
    
    def detect_encoding(self, file_path: Path) -> str:
        """检测文件编码"""
        try:
            with open(file_path, 'rb') as f:
                raw_data = f.read()
                result = chardet.detect(raw_data)
                return result.get('encoding', 'utf-8')
        except Exception:
            return 'utf-8'
    
    def read_file_with_encoding(self, file_path: Path) -> Optional[str]:
        """尝试多种编码读取文件"""
        encodings = ['utf-8', 'utf-8-sig', 'gbk', 'gb2312', 'gb18030', 'big5', 'latin1']
        
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    content = f.read()
                print(f"成功使用编码 {encoding} 读取: {file_path.name}")
                return content
            except UnicodeDecodeError:
                continue
            except Exception as e:
                print(f"读取失败 {encoding}: {e}")
                continue
        
        print(f"所有编码尝试失败: {file_path}")
        return None
    
    def fix_garbled_text(self, content: str) -> Tuple[str, int]:
        """修复乱码文本"""
        fixes = 0
        original_content = content
        
        # 应用乱码映射
        for garbled, correct in self.garbled_mappings.items():
            if garbled in content:
                content = content.replace(garbled, correct)
                fixes += 1
        
        # 修复特殊的编码问题
        # 1. 修复被"号"字符分隔的代码
        if '号' in content and 'package' in content:
            # 这是严重编码错误的文件，需要特殊处理
            content = self.fix_severely_corrupted_file(content)
            fixes += 10  # 标记为重大修复
        
        # 2. 修复注解中的乱码
        content = re.sub(r'@Schema\(description = "([^"]*嬫椂闂?[^"]*)"', 
                        r'@Schema(description = "开始时间"', content)
        content = re.sub(r'@NotNull\(message = "([^"]*翠笉鑳戒负绌?[^"]*)"', 
                        r'@NotNull(message = "开始时间不能为空"', content)
        
        # 3. 修复缺失的引号和括号
        content = re.sub(r'@Schema\(description = "([^"]*)", requiredMode = Schema\.RequiredMode\.REQUIRED\)(?!\s*")', 
                        r'@Schema(description = "\1", requiredMode = Schema.RequiredMode.REQUIRED)', content)
        
        return content, fixes
    
    def fix_severely_corrupted_file(self, content: str) -> str:
        """修复严重编码错误的文件（如VisitorRecordDO.java）"""
        # 如果文件被"号"字符完全破坏，尝试重建基本结构
        if content.count('号') > 100:  # 严重编码错误的标志
            print("检测到严重编码错误，尝试重建文件结构...")
            
            # 提取可能的包名和类名信息
            package_match = re.search(r'号p号a号c号k号a号g号e号\s+([^号]+)', content)
            if package_match:
                package_name = package_match.group(1).replace('号', '')
                
                # 生成基本的DO类结构
                return self.generate_basic_do_structure(package_name)
        
        # 否则只是移除"号"字符
        return content.replace('号', '')
    
    def generate_basic_do_structure(self, package_name: str) -> str:
        """生成基本的DO类结构"""
        return f"""package {package_name};

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 访客进出记录 DO
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@TableName(value = "visitor_record", autoResultMap = true)
@KeySequence("visitor_record_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可以不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VisitorRecordDO extends BaseDO {{

    /**
     * 记录ID
     */
    @TableId
    private Long id;

    /**
     * 访客申请ID
     */
    private Long applicationId;

    /**
     * 访客姓名
     */
    private String visitorName;

    /**
     * 访客手机号
     */
    private String visitorPhone;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 进入时间
     */
    private LocalDateTime checkInTime;

    /**
     * 离开时间
     */
    private LocalDateTime checkOutTime;

    /**
     * 记录类型：1-进入，2-离开
     */
    private Integer recordType;

    /**
     * 验证方式：1-二维码，2-手动，3-人脸识别
     */
    private Integer verificationMethod;

    /**
     * 安检结果：1-通过，2-未通过
     */
    private Integer securityCheckResult;

    /**
     * 备注
     */
    private String remark;

}}
"""

    def fix_java_file(self, file_path: Path) -> bool:
        """修复单个Java文件"""
        try:
            # 1. 备份原文件
            backup_path = self.backup_dir / file_path.relative_to(self.root_path)
            backup_path.parent.mkdir(parents=True, exist_ok=True)
            shutil.copy2(file_path, backup_path)
            
            # 2. 读取文件内容
            content = self.read_file_with_encoding(file_path)
            if content is None:
                self.failed_files.append(str(file_path))
                return False
            
            # 3. 修复乱码
            fixed_content, fixes = self.fix_garbled_text(content)
            
            # 4. 重新保存文件（UTF-8无BOM）
            with open(file_path, 'w', encoding='utf-8', newline='') as f:
                f.write(fixed_content)
            
            if fixes > 0:
                self.stats['encoding_fixes'] += fixes
                self.fixed_files.append(str(file_path))
                print(f"修复完成: {file_path.name} ({fixes}个修复)")
            
            return True
            
        except Exception as e:
            print(f"修复失败: {file_path} - {e}")
            self.failed_files.append(str(file_path))
            return False
    
    def scan_and_fix(self, target_dir: str = "yudao-module/yudao-module-visitor/yudao-module-visitor-biz"):
        """扫描并修复指定目录下的Java文件"""
        target_path = self.root_path / target_dir
        
        if not target_path.exists():
            print(f"目标目录不存在: {target_path}")
            return
        
        # 创建备份
        self.create_backup()
        
        # 查找所有Java文件
        java_files = list(target_path.rglob("*.java"))
        self.stats['total_files'] = len(java_files)
        
        print(f"找到 {len(java_files)} 个Java文件")
        
        # 修复每个文件
        for java_file in java_files:
            if self.fix_java_file(java_file):
                self.stats['fixed_files'] += 1
            else:
                self.stats['failed_files'] += 1
        
        # 输出统计信息
        self.print_summary()
    
    def print_summary(self):
        """输出修复总结"""
        print("\n" + "="*50)
        print("修复总结")
        print("="*50)
        print(f"总文件数: {self.stats['total_files']}")
        print(f"成功修复: {self.stats['fixed_files']}")
        print(f"修复失败: {self.stats['failed_files']}")
        print(f"编码修复次数: {self.stats['encoding_fixes']}")
        
        if self.failed_files:
            print(f"\n失败文件列表:")
            for file in self.failed_files:
                print(f"  - {file}")

if __name__ == "__main__":
    # 使用当前目录作为根目录
    fixer = JavaEncodingFixer(".")
    fixer.scan_and_fix()
