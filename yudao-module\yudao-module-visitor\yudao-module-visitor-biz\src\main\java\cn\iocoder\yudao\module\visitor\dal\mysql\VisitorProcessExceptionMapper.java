package cn.iocoder.yudao.module.visitor.dal.mysql;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.visitor.controller.admin.process.vo.VisitorProcessExceptionPageReqVO;
import cn.iocoder.yudao.module.visitor.dal.dataobject.VisitorProcessExceptionDO;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 访客流程异常处理记录 Mapper
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Mapper
public interface VisitorProcessExceptionMapper extends BaseMapperX<VisitorProcessExceptionDO> {

    default PageResult<VisitorProcessExceptionDO> selectPage(VisitorProcessExceptionPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<VisitorProcessExceptionDO>()
                .eqIfPresent(VisitorProcessExceptionDO::getApplicationId, reqVO.getApplicationId())
                .eqIfPresent(VisitorProcessExceptionDO::getExceptionType, reqVO.getExceptionType())
                .eqIfPresent(VisitorProcessExceptionDO::getExceptionLevel, reqVO.getExceptionLevel())
                .eqIfPresent(VisitorProcessExceptionDO::getHandlingStatus, reqVO.getHandlingStatus())
                .eqIfPresent(VisitorProcessExceptionDO::getHandlerId, reqVO.getHandlerId())
                .betweenIfPresent(VisitorProcessExceptionDO::getExceptionTime, reqVO.getExceptionTime())
                .orderByDesc(VisitorProcessExceptionDO::getId));
    }

    default List<VisitorProcessExceptionDO> selectListByApplicationId(Long applicationId) {
        return selectList(new LambdaQueryWrapperX<VisitorProcessExceptionDO>()
                .eq(VisitorProcessExceptionDO::getApplicationId, applicationId)
                .orderByDesc(VisitorProcessExceptionDO::getExceptionTime));
    }

    default List<VisitorProcessExceptionDO> selectPendingExceptions() {
        return selectList(new LambdaQueryWrapperX<VisitorProcessExceptionDO>()
                .in(VisitorProcessExceptionDO::getHandlingStatus, 1, 2)); // 寰呭鐞嗐€佸鐞嗕腑
    }

    default List<VisitorProcessExceptionDO> selectHighPriorityExceptions() {
        return selectList(new LambdaQueryWrapperX<VisitorProcessExceptionDO>()
                .in(VisitorProcessExceptionDO::getExceptionLevel, 3, 4) // 楂樸€佺揣鎬?                .in(VisitorProcessExceptionDO::getHandlingStatus, 1, 2)); // 寰呭鐞嗐€佸鐞嗕腑
    }

}

