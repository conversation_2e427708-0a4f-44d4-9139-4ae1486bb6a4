yudao-module\yudao-module-visitor\yudao-module-visitor-biz\src\main\java\cn\iocoder\yudao\module\visitor\controller\admin\training\VisitorTrainingController.java:
   32:  * 管理后台 - 访客培训 Controller
   37: @Tag(name = "管理后台 - 访客培训")
   48:     @Operation(summary = "创建访客培训")
   55:     @Operation(summary = "更新访客培训")
   63:     @Operation(summary = "删除访客培训")
   72:     @Operation(summary = "获得访客培训")
   81:     @Operation(summary = "获得访客培训分页")
   89:     @Operation(summary = "导出访客培训Excel")
   96:         ExcelUtils.write(response, "访客培训.xls", "鏁版嵁", VisitorTrainingRespVO.class,
  101:     @Operation(summary = "树规嵁访客类型鑾峰彇培训列表")

yudao-module\yudao-module-visitor\yudao-module-visitor-biz\src\main\java\cn\iocoder\yudao\module\visitor\controller\admin\training\vo\VisitorTrainingCreateReqVO.java:
  10:  * 管理后台 - 访客培训创建 Request VO
  15: @Schema(description = "管理后台 - 访客培训创建 Request VO")
  19:     @Schema(description = "培训名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "安全培训")
  20:     @NotBlank(message = "培训名称不能为空")
  23:     @Schema(description = "培训类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
  24:     @NotNull(message = "培训类型不能为空")
  31:     @Schema(description = "培训鍐呭", example = "安全操作员瑙勭▼...")
  34:     @Schema(description = "培训瑙嗛URL", example = "https://example.com/video.mp4")
  37:     @Schema(description = "培训鏂囨。URL", example = "https://example.com/doc.pdf")

yudao-module\yudao-module-visitor\yudao-module-visitor-biz\src\main\java\cn\iocoder\yudao\module\visitor\controller\admin\training\vo\VisitorTrainingPageReqVO.java:
  15:  * 管理后台 - 访客培训配置分页 Request VO
  20: @Schema(description = "管理后台 - 访客培训配置分页 Request VO")
  26:     @Schema(description = "培训名称", example = "安全培训")
  29:     @Schema(description = "培训类型", example = "1")

yudao-module\yudao-module-visitor\yudao-module-visitor-biz\src\main\java\cn\iocoder\yudao\module\visitor\controller\admin\training\vo\VisitorTrainingRecordCreateReqVO.java:
  11:  * 管理后台 - 访客培训完成记录创建 Request VO
  16: @Schema(description = "管理后台 - 访客培训完成记录创建 Request VO")
  24:     @Schema(description = "培训ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
  25:     @NotNull(message = "培训ID不能为空")
  39:     @Schema(description = "瀹為檯培训时长锛堝垎閽燂級", example = "60")
  63:     @Schema(description = "备注", example = "培训完成")

yudao-module\yudao-module-visitor\yudao-module-visitor-biz\src\main\java\cn\iocoder\yudao\module\visitor\controller\admin\training\vo\VisitorTrainingRecordPageReqVO.java:
  15:  * 管理后台 - 访客培训完成记录分页 Request VO
  20: @Schema(description = "管理后台 - 访客培训完成记录分页 Request VO")
  29:     @Schema(description = "培训ID", example = "1")

yudao-module\yudao-module-visitor\yudao-module-visitor-biz\src\main\java\cn\iocoder\yudao\module\visitor\controller\admin\training\vo\VisitorTrainingRecordUpdateReqVO.java:
  11:  * 管理后台 - 访客培训完成记录更新 Request VO
  16: @Schema(description = "管理后台 - 访客培训完成记录更新 Request VO")

yudao-module\yudao-module-visitor\yudao-module-visitor-biz\src\main\java\cn\iocoder\yudao\module\visitor\controller\admin\training\vo\VisitorTrainingRespVO.java:
   9:  * 管理后台 - 访客培训 Response VO
  14: @Schema(description = "管理后台 - 访客培训 Response VO")
  21:     @Schema(description = "培训名称", example = "安全培训")
  24:     @Schema(description = "培训类型", example = "1")
  30:     @Schema(description = "培训鍐呭", example = "安全操作员瑙勭▼...")
  33:     @Schema(description = "培训瑙嗛URL", example = "https://example.com/video.mp4")
  36:     @Schema(description = "培训鏂囨。URL", example = "https://example.com/doc.pdf")

yudao-module\yudao-module-visitor\yudao-module-visitor-biz\src\main\java\cn\iocoder\yudao\module\visitor\controller\admin\training\vo\VisitorTrainingUpdateReqVO.java:
  11:  * 管理后台 - 访客培训更新 Request VO
  16: @Schema(description = "管理后台 - 访客培训更新 Request VO")

yudao-module\yudao-module-visitor\yudao-module-visitor-biz\src\main\java\cn\iocoder\yudao\module\visitor\controller\app\application\AppVisitorApplicationController.java:
  88:     @Operation(summary = "鑾峰彇培训进度")
  92:         // 杩欓噷库旇璋冪敤培训服务鑾峰彇进度

yudao-module\yudao-module-visitor\yudao-module-visitor-biz\src\main\java\cn\iocoder\yudao\module\visitor\controller\app\training\AppVisitorTrainingController.java:
  26:  * 用户 APP - 访客培训 Controller
  31: @Tag(name = "用户 APP - 访客培训")
  45:     @Operation(summary = "鑾峰彇培训列表")
  54:     @Operation(summary = "鑾峰彇培训璇︽儏")
  55:     @Parameter(name = "id", description = "培训ID", required = true)
  73:     @Operation(summary = "完成培训")
  82:     @Operation(summary = "鑾峰彇培训进度")

yudao-module\yudao-module-visitor\yudao-module-visitor-biz\src\main\java\cn\iocoder\yudao\module\visitor\controller\app\training\vo\AppTrainingCompleteReqVO.java:
   9:  * 鐢ㄦ埛 APP - 培训完成 Request VO
  14: @Schema(description = "鐢ㄦ埛 APP - 培训完成 Request VO")
  18:     @Schema(description = "培训记录ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
  19:     @NotNull(message = "培训记录ID不能为空")

yudao-module\yudao-module-visitor\yudao-module-visitor-biz\src\main\java\cn\iocoder\yudao\module\visitor\controller\app\training\vo\AppTrainingProgressRespVO.java:
   7:  * 用户 APP - 培训进度 Response VO
  12: @Schema(description = "用户 APP - 培训进度 Response VO")

yudao-module\yudao-module-visitor\yudao-module-visitor-biz\src\main\java\cn\iocoder\yudao\module\visitor\controller\app\training\vo\AppTrainingRespVO.java:
   7:  * 用户 APP - 培训信息 Response VO
  12: @Schema(description = "用户 APP - 培训信息 Response VO")
  16:     @Schema(description = "培训ID", example = "1024")
  19:     @Schema(description = "培训名称", example = "安全培训")
  22:     @Schema(description = "培训类型", example = "1")
  25:     @Schema(description = "培训鍐呭", example = "安全操作员瑙勭▼...")
  28:     @Schema(description = "培训瑙嗛URL", example = "https://example.com/video.mp4")
  31:     @Schema(description = "培训鏂囨。URL", example = "https://example.com/doc.pdf")

yudao-module\yudao-module-visitor\yudao-module-visitor-biz\src\main\java\cn\iocoder\yudao\module\visitor\convert\training\VisitorTrainingConvert.java:
  15:  * 访客培训 Convert

yudao-module\yudao-module-visitor\yudao-module-visitor-biz\src\main\java\cn\iocoder\yudao\module\visitor\dal\dataobject\VisitorApplicationDO.java:
  159:      * 鏄惁完成培训锛?-鍚?1-鏄?     */
  163:      * 培训完成时间
  168:      * 培训绛惧瓧图剧墖URL

yudao-module\yudao-module-visitor\yudao-module-visitor-biz\src\main\java\cn\iocoder\yudao\module\visitor\dal\dataobject\VisitorProcessExceptionDO.java:
  211:      * 缁忛獙鏁欒鎬荤粨

yudao-module\yudao-module-visitor\yudao-module-visitor-biz\src\main\java\cn\iocoder\yudao\module\visitor\dal\dataobject\VisitorTrainingDO.java:
   15:  * 访客培训配置 DO
   37:      * 培训名称
   42:      * 培训类型
   49:      * 培训鍐呭
   54:      * 培训鏉愭枡URL数组锛歔{type:pdf/video/image,url,name,size}]
  107:      * 培训鏉愭枡鍐呴儴类?     */

yudao-module\yudao-module-visitor\yudao-module-visitor-biz\src\main\java\cn\iocoder\yudao\module\visitor\dal\dataobject\VisitorTrainingRecordDO.java:
  16:  * 访客培训完成记录 DO
  43:      * 培训ID
  62:      * 瀹為檯培训时长锛堝垎閽燂級

yudao-module\yudao-module-visitor\yudao-module-visitor-biz\src\main\java\cn\iocoder\yudao\module\visitor\dal\mysql\VisitorTrainingMapper.java:
   14:  * 访客培训配置 Mapper
   97:      * 统计培训配置鏁伴噺鎸夊煿璁被鍨嬪垎缁?     */
  101:      * 统计培训配置鏁伴噺鎸夎瀹㈢被鍨嬪垎缁?     */

yudao-module\yudao-module-visitor\yudao-module-visitor-biz\src\main\java\cn\iocoder\yudao\module\visitor\dal\mysql\VisitorTrainingRecordMapper.java:
   16:  * 访客培训完成记录 Mapper
  102:      * 统计培训记录鏁伴噺鎸夊畬鎴愮姸鎬佸垎缁?     */
  106:      * 统计培训记录鏁伴噺鎸夊煿璁璉D列嗙粍
  111:      * 查询鎸囧畾时间鑼冨洿鍐呯殑培训完成统计

yudao-module\yudao-module-visitor\yudao-module-visitor-biz\src\main\java\cn\iocoder\yudao\module\visitor\framework\flowable\delegate\VisitorQrCodeDelegate.java:
  51:                     log.info("[execute] 培训鏈畬鎴愶紝鏆備笉生成二维码?);

yudao-module\yudao-module-visitor\yudao-module-visitor-biz\src\main\java\cn\iocoder\yudao\module\visitor\service\application\VisitorApplicationServiceImpl.java:
  304:         // 校验培训鏄惁完成

yudao-module\yudao-module-visitor\yudao-module-visitor-biz\src\main\java\cn\iocoder\yudao\module\visitor\service\training\VisitorTrainingRecordService.java:
   14:  * 访客培训完成记录 Service 接口
   22:      * 创建访客培训完成记录
   30:      * 更新访客培训完成记录
   37:      * 删除访客培训完成记录
   44:      * 获得访客培训完成记录
   47:      * @return 访客培训完成记录
   52:      * 获得访客培训完成记录列表
   55:      * @return 访客培训完成记录列表
   60:      * 获得访客培训完成记录分页
   63:      * @return 访客培训完成记录分页
   68:      * 获得访客培训完成记录列表, 用于 Excel 导出
   71:      * @return 访客培训完成记录列表
   78:      * @param trainingId 培训ID
   80:      * @return 培训记录ID
   85:      * 完成培训
  103:      * 鑾峰彇访客培训进度
  106:      * @return 培训进度信息

yudao-module\yudao-module-visitor\yudao-module-visitor-biz\src\main\java\cn\iocoder\yudao\module\visitor\service\training\VisitorTrainingRecordServiceImpl.java:
   28:  * 璁垮鍩硅完成记录 Service 实现类? *
   46:         log.info("[createVisitorTrainingRecord] 创建璁垮鍩硅记录锛屽弬鏁帮細{}", createReqVO);
   48:         // 插入鍩硅记录
   52:         log.info("[createVisitorTrainingRecord] 璁垮鍩硅记录创建成功锛孖D锛歿}", trainingRecord.getId());
   60:         log.info("[updateVisitorTrainingRecord] 更新璁垮鍩硅记录锛屽弬鏁帮細{}", updateReqVO);
   65:         // 更新鍩硅记录
   69:         log.info("[updateVisitorTrainingRecord] 璁垮鍩硅记录更新成功锛孖D锛歿}", updateReqVO.getId());
   75:         log.info("[deleteVisitorTrainingRecord] 删除璁垮鍩硅记录锛孖D锛歿}", id);
   83:         log.info("[deleteVisitorTrainingRecord] 璁垮鍩硅记录删除成功锛孖D锛歿}", id);
  119:         // 妫€鏌ユ槸鍚﹀凡瀛樺湪鍩硅记录
  123:             log.warn("[startTraining] 鍩硅记录宸插瓨鍦紝记录ID锛歿}", existingRecord.getId());
  127:         // 创建鍩硅记录
  137:         log.info("[startTraining] 鍩硅寮€濮嬭褰曟垚鍔燂紝记录ID锛歿}", record.getId());
  145:         log.info("[completeTraining] 完成鍩硅锛岃褰旾D锛歿}锛岃€冭瘯列嗘暟锛歿}", recordId, examScore);
  154:         // 鑾峰彇鍩硅配置
  167:         // 计算鍩硅鏃堕暱
  175:         // 更新鍩硅记录
  186:         log.info("[completeTraining] 鍩硅完成记录成功锛岃褰旾D锛歿}锛屾椂闀匡細{}列嗛挓", recordId, duration);
  193:         // 鑾峰彇蹇呬慨鍩硅列楄〃
  196:             return true; // 娌℃湁蹇呬慨鍩硅
  199:         // 鑾峰彇宸插畬鎴愮殑鍩硅记录
  208:                 log.info("[isAllRequiredTrainingCompleted] 蹇呬慨鍩硅鏈畬鎴愶紝鍩硅ID锛歿}", requiredTraining.getId());
  219:         log.info("[getTrainingProgress] 鑾峰彇鍩硅杩涘害锛岀敵璇稩D锛歿}", applicationId);
  221:         // 鑾峰彇鍩硅记录

yudao-module\yudao-module-visitor\yudao-module-visitor-biz\src\main\java\cn\iocoder\yudao\module\visitor\service\training\VisitorTrainingService.java:
   14:  * 访客培训 Service 接口
   22:      * 创建访客培训
   30:      * 更新访客培训
   37:      * 删除访客培训
   44:      * 获得访客培训
   47:      * @return 访客培训
   52:      * 获得访客培训列表
   55:      * @return 访客培训列表
   60:      * 获得访客培训分页
   63:      * @return 访客培训分页
   68:      * 获得访客培训列表, 用于 Excel 导出
   71:      * @return 访客培训列表
   76:      * 树规嵁访客类型鑾峰彇培训列表
   79:      * @return 培训列表
   84:      * 树规嵁培训类型鑾峰彇培训列表
   86:      * @param trainingType 培训类型
   87:      * @return 培训列表
   92:      * 鑾峰彇必修培训列表
   95:      * @return 必修培训列表
  100:      * 鍚敤/禁用培训
  102:      * @param id 培训ID

yudao-module\yudao-module-visitor\yudao-module-visitor-biz\src\main\java\cn\iocoder\yudao\module\visitor\service\training\VisitorTrainingServiceImpl.java:
   24:  * 访客培训 Service 实现类? *
   39:         log.info("[createVisitorTraining] 创建访客培训锛屽弬鏁帮細{}", createReqVO);
   45:         log.info("[createVisitorTraining] 创建访客培训成功锛孖D锛歿}", visitorTraining.getId());
   52:         log.info("[updateVisitorTraining] 更新访客培训锛屽弬鏁帮細{}", updateReqVO);
   61:         log.info("[updateVisitorTraining] 更新访客培训成功锛孖D锛歿}", updateReqVO.getId());
   67:         log.info("[deleteVisitorTraining] 删除访客培训锛孖D锛歿}", id);
   75:         log.info("[deleteVisitorTraining] 删除访客培训成功锛孖D锛歿}", id);
  122:         log.info("[updateTrainingStatus] 更新培训状态侊紝ID锛歿}锛岀姸鎬侊細{}", id, status);
  132:         log.info("[updateTrainingStatus] 培训状态佹洿鏂版垚鍔燂紝ID锛歿}锛屾柊状态侊細{}", id, status);
