package cn.iocoder.yudao.module.visitor.dal.mysql;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.visitor.dal.dataobject.VisitorApprovalOpinionDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 访客娴佺▼瀹℃壒鎰忚 Mapper
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Mapper
public interface VisitorApprovalOpinionMapper extends BaseMapperX<VisitorApprovalOpinionDO> {

    default List<VisitorApprovalOpinionDO> selectListByApplicationId(Long applicationId) {
        return selectList(new LambdaQueryWrapperX<VisitorApprovalOpinionDO>()
                .eq(VisitorApprovalOpinionDO::getApplicationId, applicationId)
                .orderByAsc(VisitorApprovalOpinionDO::getApprovalLevel)
                .orderByAsc(VisitorApprovalOpinionDO::getApprovalSequence));
    }

    default List<VisitorApprovalOpinionDO> selectListByTaskId(String taskId) {
        return selectList(VisitorApprovalOpinionDO::getTaskId, taskId);
    }

    default List<VisitorApprovalOpinionDO> selectListByApproverId(Long approverId) {
        return selectList(VisitorApprovalOpinionDO::getApproverId, approverId);
    }

}

