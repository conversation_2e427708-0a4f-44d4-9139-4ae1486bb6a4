package cn.iocoder.yudao.module.visitor.controller.admin.training.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import jakarta.validation.constraints.NotNull;

/**
 * 管理后台 - 访客培训更新 Request VO
 *
 * <AUTHOR>
 * @since 2025-01-12
 */
@Schema(description = "管理后台 - 访客培训更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class VisitorTrainingUpdateReqVO extends VisitorTrainingCreateReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "主键ID不能为空")
    private Long id;

}

