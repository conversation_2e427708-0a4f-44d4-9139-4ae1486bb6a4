package cn.iocoder.yudao.module.visitor.convert.application;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.visitor.controller.admin.application.vo.VisitorApplicationCreateReqVO;
import cn.iocoder.yudao.module.visitor.controller.admin.application.vo.VisitorApplicationRespVO;
import cn.iocoder.yudao.module.visitor.controller.admin.application.vo.VisitorApplicationUpdateReqVO;
import cn.iocoder.yudao.module.visitor.dal.dataobject.VisitorApplicationDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 访客申请 Convert
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Mapper
public interface VisitorApplicationConvert {

    VisitorApplicationConvert INSTANCE = Mappers.getMapper(VisitorApplicationConvert.class);

    VisitorApplicationDO convert(VisitorApplicationCreateReqVO bean);

    VisitorApplicationDO convert(VisitorApplicationUpdateReqVO bean);

    VisitorApplicationRespVO convert(VisitorApplicationDO bean);

    List<VisitorApplicationRespVO> convertList(List<VisitorApplicationDO> list);

    PageResult<VisitorApplicationRespVO> convertPage(PageResult<VisitorApplicationDO> page);

}

