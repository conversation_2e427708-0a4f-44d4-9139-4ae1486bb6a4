package cn.iocoder.yudao.module.visitor.controller.admin.application;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.module.visitor.controller.admin.application.vo.*;
import cn.iocoder.yudao.module.visitor.convert.application.VisitorApplicationConvert;
import cn.iocoder.yudao.module.visitor.dal.dataobject.VisitorApplicationDO;
import cn.iocoder.yudao.module.visitor.service.application.VisitorApplicationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

/**
 * 管理后台 - 访客申请 Controller
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Tag(name = "管理后台 - 访客申请")
@RestController
@RequestMapping("/visitor/application")
@Validated
@Slf4j
public class VisitorApplicationController {

    @Resource
    private VisitorApplicationService visitorApplicationService;

    @PostMapping("/create")
    @Operation(summary = "创建访客申请")
    @PreAuthorize("@ss.hasPermission('visitor:application:create')")
    @ApiAccessLog(operateType = CREATE)
    public CommonResult<Long> createVisitorApplication(@Valid @RequestBody VisitorApplicationCreateReqVO createReqVO) {
        return success(visitorApplicationService.createVisitorApplication(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新访客申请")
    @PreAuthorize("@ss.hasPermission('visitor:application:update')")
    @ApiAccessLog(operateType = UPDATE)
    public CommonResult<Boolean> updateVisitorApplication(@Valid @RequestBody VisitorApplicationUpdateReqVO updateReqVO) {
        visitorApplicationService.updateVisitorApplication(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除访客申请")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('visitor:application:delete')")
    @ApiAccessLog(operateType = DELETE)
    public CommonResult<Boolean> deleteVisitorApplication(@RequestParam("id") Long id) {
        visitorApplicationService.deleteVisitorApplication(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得访客申请")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('visitor:application:query')")
    public CommonResult<VisitorApplicationRespVO> getVisitorApplication(@RequestParam("id") Long id) {
        VisitorApplicationDO visitorApplication = visitorApplicationService.getVisitorApplication(id);
        return success(BeanUtils.toBean(visitorApplication, VisitorApplicationRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得访客申请分页")
    @PreAuthorize("@ss.hasPermission('visitor:application:query')")
    public CommonResult<PageResult<VisitorApplicationRespVO>> getVisitorApplicationPage(@Valid VisitorApplicationPageReqVO pageReqVO) {
        PageResult<VisitorApplicationDO> pageResult = visitorApplicationService.getVisitorApplicationPage(pageReqVO);
        return success(VisitorApplicationConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出访客申请 Excel")
    @PreAuthorize("@ss.hasPermission('visitor:application:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportVisitorApplicationExcel(@Valid VisitorApplicationPageReqVO exportReqVO,
                                              HttpServletResponse response) throws IOException {
        exportReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<VisitorApplicationDO> list = visitorApplicationService.getVisitorApplicationList(exportReqVO);
        // 导出 Excel
        ExcelUtils.write(response, "访客申请.xls", "数据", VisitorApplicationRespVO.class,
                BeanUtils.toBean(list, VisitorApplicationRespVO.class));
    }

    @PostMapping("/submit")
    @Operation(summary = "提交访客申请")
    @Parameter(name = "id", description = "申请ID", required = true)
    @PreAuthorize("@ss.hasPermission('visitor:application:submit')")
    @ApiAccessLog(operateType = UPDATE)
    public CommonResult<String> submitVisitorApplication(@RequestParam("id") Long id) {
        String processInstanceId = visitorApplicationService.submitVisitorApplication(id);
        return success(processInstanceId);
    }

    @PostMapping("/confirm")
    @Operation(summary = "联系人确认访客申请")
    @PreAuthorize("@ss.hasPermission('visitor:application:confirm')")
    @ApiAccessLog(operateType = UPDATE)
    public CommonResult<Boolean> confirmVisitorApplication(@Valid @RequestBody VisitorApplicationConfirmReqVO confirmReqVO) {
        visitorApplicationService.confirmVisitorApplication(confirmReqVO.getId(), confirmReqVO.getConfirmed(), confirmReqVO.getReason());
        return success(true);
    }

    @PostMapping("/approve")
    @Operation(summary = "审批访客申请")
    @PreAuthorize("@ss.hasPermission('visitor:application:approve')")
    @ApiAccessLog(operateType = UPDATE)
    public CommonResult<Boolean> approveVisitorApplication(@Valid @RequestBody VisitorApplicationApproveReqVO approveReqVO) {
        visitorApplicationService.approveVisitorApplication(approveReqVO.getId(), approveReqVO.getApproved(), approveReqVO.getReason());
        return success(true);
    }

    @PostMapping("/generate-qr-code")
    @Operation(summary = "生成访客二维码")
    @Parameter(name = "id", description = "申请ID", required = true)
    @PreAuthorize("@ss.hasPermission('visitor:application:qr-code')")
    @ApiAccessLog(operateType = UPDATE)
    public CommonResult<String> generateVisitorQrCode(@RequestParam("id") Long id) {
        String qrCodeUrl = visitorApplicationService.generateVisitorQrCode(id);
        return success(qrCodeUrl);
    }

    @PostMapping("/entry")
    @Operation(summary = "访客入园登记")
    @PreAuthorize("@ss.hasPermission('visitor:application:entry')")
    @ApiAccessLog(operateType = UPDATE)
    public CommonResult<Boolean> entryVisitor(@Valid @RequestBody VisitorApplicationEntryReqVO entryReqVO) {
        visitorApplicationService.entryVisitor(entryReqVO.getId(), entryReqVO.getOperatorId(),
                entryReqVO.getGateLocation(), entryReqVO.getVerificationMethod(),
                entryReqVO.getTemperature(), entryReqVO.getRemarks());
        return success(true);
    }

    @PostMapping("/exit")
    @Operation(summary = "访客出园登记")
    @PreAuthorize("@ss.hasPermission('visitor:application:exit')")
    @ApiAccessLog(operateType = UPDATE)
    public CommonResult<Boolean> exitVisitor(@Valid @RequestBody VisitorApplicationExitReqVO exitReqVO) {
        visitorApplicationService.exitVisitor(exitReqVO.getId(), exitReqVO.getOperatorId(),
                exitReqVO.getGateLocation(), exitReqVO.getRemarks());
        return success(true);
    }

    @PostMapping("/cancel")
    @Operation(summary = "取消访客申请")
    @PreAuthorize("@ss.hasPermission('visitor:application:cancel')")
    @ApiAccessLog(operateType = UPDATE)
    public CommonResult<Boolean> cancelVisitorApplication(@Valid @RequestBody VisitorApplicationCancelReqVO cancelReqVO) {
        visitorApplicationService.cancelVisitorApplication(cancelReqVO.getId(), cancelReqVO.getReason());
        return success(true);
    }

    @GetMapping("/statistics")
    @Operation(summary = "获取访客申请统计")
    @PreAuthorize("@ss.hasPermission('visitor:application:statistics')")
    public CommonResult<Object> getVisitorApplicationStatistics(
            @RequestParam(value = "startTime", required = false) LocalDateTime startTime,
            @RequestParam(value = "endTime", required = false) LocalDateTime endTime) {
        Object statistics = visitorApplicationService.getVisitorApplicationStatistics(startTime, endTime);
        return success(statistics);
    }

}

