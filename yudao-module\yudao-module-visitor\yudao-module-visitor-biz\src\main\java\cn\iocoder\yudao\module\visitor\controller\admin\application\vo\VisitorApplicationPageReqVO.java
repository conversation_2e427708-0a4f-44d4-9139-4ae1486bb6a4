package cn.iocoder.yudao.module.visitor.controller.admin.application.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 管理后台 - 访客申请分页 Request VO
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Schema(description = "管理后台 - 访客申请分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class VisitorApplicationPageReqVO extends PageParam {

    @Schema(description = "访客类型", example = "1")
    private Integer visitorType;

    @Schema(description = "访客姓名", example = "张三")
    private String visitorName;

    @Schema(description = "申请状态", example = "1")
    private Integer status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
