package cn.iocoder.yudao.module.visitor.dal.mysql;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.visitor.controller.admin.record.vo.VisitorRecordPageReqVO;
import cn.iocoder.yudao.module.visitor.dal.dataobject.VisitorRecordDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * 访客进出记录 Mapper
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Mapper
public interface VisitorRecordMapper extends BaseMapperX<VisitorRecordDO> {

    default PageResult<VisitorRecordDO> selectPage(VisitorRecordPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<VisitorRecordDO>()
                .eqIfPresent(VisitorRecordDO::getApplicationId, reqVO.getApplicationId())
                .likeIfPresent(VisitorRecordDO::getVisitorName, reqVO.getVisitorName())
                .likeIfPresent(VisitorRecordDO::getVisitorPhone, reqVO.getVisitorPhone())
                .eqIfPresent(VisitorRecordDO::getOperationType, reqVO.getOperationType())
                .eqIfPresent(VisitorRecordDO::getOperatorId, reqVO.getOperatorId())
                .likeIfPresent(VisitorRecordDO::getOperatorName, reqVO.getOperatorName())
                .likeIfPresent(VisitorRecordDO::getGateLocation, reqVO.getGateLocation())
                .eqIfPresent(VisitorRecordDO::getVerificationMethod, reqVO.getVerificationMethod())
                .likeIfPresent(VisitorRecordDO::getVehiclePlate, reqVO.getVehiclePlate())
                .eqIfPresent(VisitorRecordDO::getHealthStatus, reqVO.getHealthStatus())
                .eqIfPresent(VisitorRecordDO::getSecurityCheckResult, reqVO.getSecurityCheckResult())
                .betweenIfPresent(VisitorRecordDO::getOperationTime, reqVO.getOperationTime())
                .betweenIfPresent(VisitorRecordDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(VisitorRecordDO::getId));
    }

    default List<VisitorRecordDO> selectList(VisitorRecordPageReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<VisitorRecordDO>()
                .eqIfPresent(VisitorRecordDO::getApplicationId, reqVO.getApplicationId())
                .likeIfPresent(VisitorRecordDO::getVisitorName, reqVO.getVisitorName())
                .likeIfPresent(VisitorRecordDO::getVisitorPhone, reqVO.getVisitorPhone())
                .eqIfPresent(VisitorRecordDO::getOperationType, reqVO.getOperationType())
                .eqIfPresent(VisitorRecordDO::getOperatorId, reqVO.getOperatorId())
                .likeIfPresent(VisitorRecordDO::getOperatorName, reqVO.getOperatorName())
                .likeIfPresent(VisitorRecordDO::getGateLocation, reqVO.getGateLocation())
                .eqIfPresent(VisitorRecordDO::getVerificationMethod, reqVO.getVerificationMethod())
                .likeIfPresent(VisitorRecordDO::getVehiclePlate, reqVO.getVehiclePlate())
                .eqIfPresent(VisitorRecordDO::getHealthStatus, reqVO.getHealthStatus())
                .eqIfPresent(VisitorRecordDO::getSecurityCheckResult, reqVO.getSecurityCheckResult())
                .betweenIfPresent(VisitorRecordDO::getOperationTime, reqVO.getOperationTime())
                .betweenIfPresent(VisitorRecordDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(VisitorRecordDO::getId));
    }

    default List<VisitorRecordDO> selectListByApplicationId(Long applicationId) {
        return selectList(VisitorRecordDO::getApplicationId, applicationId);
    }

    default List<VisitorRecordDO> selectListByApplicationIds(Collection<Long> applicationIds) {
        return selectList(new LambdaQueryWrapperX<VisitorRecordDO>()
                .inIfPresent(VisitorRecordDO::getApplicationId, applicationIds));
    }

    default List<VisitorRecordDO> selectListByOperationType(Integer operationType) {
        return selectList(VisitorRecordDO::getOperationType, operationType);
    }

    default List<VisitorRecordDO> selectListByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        return selectList(new LambdaQueryWrapperX<VisitorRecordDO>()
                .ge(VisitorRecordDO::getOperationTime, startTime)
                .le(VisitorRecordDO::getOperationTime, endTime));
    }

    default VisitorRecordDO selectLatestByApplicationIdAndType(Long applicationId, Integer operationType) {
        return selectOne(new LambdaQueryWrapperX<VisitorRecordDO>()
                .eq(VisitorRecordDO::getApplicationId, applicationId)
                .eq(VisitorRecordDO::getOperationType, operationType)
                .orderByDesc(VisitorRecordDO::getOperationTime)
                .last("LIMIT 1"));
    }

    default List<VisitorRecordDO> selectAbnormalRecords() {
        return selectList(new LambdaQueryWrapperX<VisitorRecordDO>()
                .isNotNull(VisitorRecordDO::getAbnormalInfo)
                .or()
                .eq(VisitorRecordDO::getHealthStatus, 2)
                .or()
                .eq(VisitorRecordDO::getSecurityCheckResult, 2));
    }

    /**
     * 统计进出记录鏁伴噺鎸夋搷浣滅被鍨嬪垎缁?     */
    List<Object[]> selectCountGroupByOperationType();

    /**
     * 统计进出记录鏁伴噺鎸夐棬宀椾綅缃垎缁?     */
    List<Object[]> selectCountGroupByGateLocation();

    /**
     * 统计进出记录鏁伴噺鎸夐獙璇佹柟寮忓垎缁?     */
    List<Object[]> selectCountGroupByVerificationMethod();

    /**
     * 查询鎸囧畾时间鑼冨洿鍐呯殑进出记录统计
     */
    Long selectCountByTimeRange(@Param("startTime") LocalDateTime startTime, 
                               @Param("endTime") LocalDateTime endTime,
                               @Param("operationType") Integer operationType);

    /**
     * 查询褰撳墠鍦ㄥ洯访客鏁伴噺
     */
    Long selectCurrentVisitorCount();

}

