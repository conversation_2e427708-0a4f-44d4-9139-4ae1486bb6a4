package cn.iocoder.yudao.module.visitor.controller.admin.training;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.module.visitor.controller.admin.training.vo.*;
import cn.iocoder.yudao.module.visitor.convert.training.VisitorTrainingConvert;
import cn.iocoder.yudao.module.visitor.dal.dataobject.VisitorTrainingDO;
import cn.iocoder.yudao.module.visitor.service.training.VisitorTrainingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

/**
 * 管理后台 - 访客培训 Controller
 *
 * <AUTHOR>
 * @since 2025-01-12
 */
@Tag(name = "管理后台 - 访客培训")
@RestController
@RequestMapping("/visitor/training")
@Validated
@Slf4j
public class VisitorTrainingController {

    @Resource
    private VisitorTrainingService visitorTrainingService;

    @PostMapping("/create")
    @Operation(summary = "创建访客培训")
    @PreAuthorize("@ss.hasPermission('visitor:training:create')")
    public CommonResult<Long> createVisitorTraining(@Valid @RequestBody VisitorTrainingCreateReqVO createReqVO) {
        return success(visitorTrainingService.createVisitorTraining(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新访客培训")
    @PreAuthorize("@ss.hasPermission('visitor:training:update')")
    public CommonResult<Boolean> updateVisitorTraining(@Valid @RequestBody VisitorTrainingUpdateReqVO updateReqVO) {
        visitorTrainingService.updateVisitorTraining(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除访客培训")
    @Parameter(name = "id", description = "编", required = true)
    @PreAuthorize("@ss.hasPermission('visitor:training:delete')")
    public CommonResult<Boolean> deleteVisitorTraining(@RequestParam("id") Long id) {
        visitorTrainingService.deleteVisitorTraining(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得访客培训")
    @Parameter(name = "id", description = "编", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('visitor:training:query')")
    public CommonResult<VisitorTrainingRespVO> getVisitorTraining(@RequestParam("id") Long id) {
        VisitorTrainingDO visitorTraining = visitorTrainingService.getVisitorTraining(id);
        return success(BeanUtils.toBean(visitorTraining, VisitorTrainingRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得访客培训分页")
    @PreAuthorize("@ss.hasPermission('visitor:training:query')")
    public CommonResult<PageResult<VisitorTrainingRespVO>> getVisitorTrainingPage(@Valid VisitorTrainingPageReqVO pageReqVO) {
        PageResult<VisitorTrainingDO> pageResult = visitorTrainingService.getVisitorTrainingPage(pageReqVO);
        return success(VisitorTrainingConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出访客培训Excel")
    @PreAuthorize("@ss.hasPermission('visitor:training:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportVisitorTrainingExcel(@Valid VisitorTrainingPageReqVO pageReqVO,
                                          HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<VisitorTrainingDO> list = visitorTrainingService.getVisitorTrainingList(pageReqVO);
        ExcelUtils.write(response, "访客培训.xls", "鏁版嵁", VisitorTrainingRespVO.class,
                VisitorTrainingConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/by-visitor-type")
    @Operation(summary = "树规嵁访客类型鑾峰彇培训列表")
    @Parameter(name = "visitorType", description = "访客类型", required = true)
    @PreAuthorize("@ss.hasPermission('visitor:training:query')")
    public CommonResult<List<VisitorTrainingRespVO>> getTrainingByVisitorType(@RequestParam("visitorType") Integer visitorType) {
        List<VisitorTrainingDO> list = visitorTrainingService.getTrainingByVisitorType(visitorType);
        return success(BeanUtils.toBean(list, VisitorTrainingRespVO.class));
    }

}

