package cn.iocoder.yudao.module.visitor.controller.admin.process.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 管理后台 - 访客流程操作员记录分页 Request VO
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Schema(description = "管理后台 - 访客流程操作员记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class VisitorProcessOperationPageReqVO extends PageParam {

    @Schema(description = "申请单ID", example = "1024")
    private Long applicationId;

    @Schema(description = "Flowable流程实例ID", example = "proc_inst_123")
    private String processInstanceId;

    @Schema(description = "Flowable任务ID", example = "task_123")
    private String taskId;

    @Schema(description = "操作员类型", example = "1")
    private Integer operationType;

    @Schema(description = "操作员结果", example = "1")
    private Integer operationResult;

    @Schema(description = "操作员人ID", example = "1")
    private Long operatorId;

    @Schema(description = "操作员人姓名", example = "张三")
    private String operatorName;

    @Schema(description = "操作员人类型", example = "1")
    private Integer operatorType;

    @Schema(description = "操作员时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] operationTime;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}

