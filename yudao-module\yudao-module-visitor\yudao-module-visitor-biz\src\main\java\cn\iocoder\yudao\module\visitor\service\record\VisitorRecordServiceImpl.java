package cn.iocoder.yudao.module.visitor.service.record;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.visitor.controller.admin.record.vo.VisitorRecordCreateReqVO;
import cn.iocoder.yudao.module.visitor.controller.admin.record.vo.VisitorRecordPageReqVO;
import cn.iocoder.yudao.module.visitor.controller.admin.record.vo.VisitorRecordUpdateReqVO;
import cn.iocoder.yudao.module.visitor.controller.app.guard.vo.AppVisitorCheckInReqVO;
import cn.iocoder.yudao.module.visitor.controller.app.guard.vo.AppVisitorCheckOutReqVO;
import cn.iocoder.yudao.module.visitor.dal.dataobject.VisitorRecordDO;
import cn.iocoder.yudao.module.visitor.dal.mysql.VisitorRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static cn.iocoder.yudao.module.visitor.enums.ErrorCodeConstants.*;

/**
 * 访客进出记录 Service 实现类? *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Service
@Validated
@Slf4j
public class VisitorRecordServiceImpl implements VisitorRecordService {

    @Resource
    private VisitorRecordMapper visitorRecordMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createVisitorRecord(@Valid VisitorRecordCreateReqVO createReqVO) {
        log.info("[createVisitorRecord] 创建访客进出记录锛屽弬鏁帮細{}", createReqVO);
        
        // 鎻掑叆访客记录
        VisitorRecordDO visitorRecord = BeanUtils.toBean(createReqVO, VisitorRecordDO.class);
        visitorRecordMapper.insert(visitorRecord);
        
        log.info("[createVisitorRecord] 访客进出记录创建成功锛孖D锛歿}", visitorRecord.getId());
        
        return visitorRecord.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateVisitorRecord(@Valid VisitorRecordUpdateReqVO updateReqVO) {
        log.info("[updateVisitorRecord] 更新访客进出记录锛屽弬鏁帮細{}", updateReqVO);
        
        // 校验存在
        validateVisitorRecordExists(updateReqVO.getId());
        
        // 更新访客记录
        VisitorRecordDO updateObj = BeanUtils.toBean(updateReqVO, VisitorRecordDO.class);
        visitorRecordMapper.updateById(updateObj);
        
        log.info("[updateVisitorRecord] 访客进出记录更新成功锛孖D锛歿}", updateReqVO.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteVisitorRecord(Long id) {
        log.info("[deleteVisitorRecord] 删除访客进出记录锛孖D锛歿}", id);
        
        // 校验存在
        validateVisitorRecordExists(id);
        
        // 删除
        visitorRecordMapper.deleteById(id);
        
        log.info("[deleteVisitorRecord] 访客进出记录删除成功锛孖D锛歿}", id);
    }

    private VisitorRecordDO validateVisitorRecordExists(Long id) {
        VisitorRecordDO record = visitorRecordMapper.selectById(id);
        if (record == null) {
            throw exception(VISITOR_RECORD_NOT_EXISTS);
        }
        return record;
    }

    @Override
    public VisitorRecordDO getVisitorRecord(Long id) {
        return visitorRecordMapper.selectById(id);
    }

    @Override
    public List<VisitorRecordDO> getVisitorRecordList(Collection<Long> ids) {
        return visitorRecordMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<VisitorRecordDO> getVisitorRecordPage(VisitorRecordPageReqVO pageReqVO) {
        return visitorRecordMapper.selectPage(pageReqVO);
    }

    @Override
    public List<VisitorRecordDO> getVisitorRecordList(VisitorRecordPageReqVO exportReqVO) {
        return visitorRecordMapper.selectList(exportReqVO);
    }

    @Override
    public List<VisitorRecordDO> getVisitorRecordsByApplicationId(Long applicationId) {
        return visitorRecordMapper.selectListByApplicationId(applicationId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long recordVisitorEntry(Long applicationId, Long operatorId, String gateLocation, 
                                  Integer verificationMethod, Double temperature, String remarks) {
        log.info("[recordVisitorEntry] 记录访客入园锛岀敵璇稩D锛歿}锛屾搷浣滃憳锛歿}锛岄棬宀楋細{}", 
                applicationId, operatorId, gateLocation);
        
        // 创建入园记录
        VisitorRecordDO record = new VisitorRecordDO();
        record.setApplicationId(applicationId);
        record.setOperationType(1); // 入园
        record.setOperationTime(LocalDateTime.now());
        record.setOperatorId(operatorId);
        record.setGateLocation(gateLocation);
        record.setVerificationMethod(verificationMethod);
        record.setTemperature(temperature != null ? BigDecimal.valueOf(temperature) : null);
        record.setHealthStatus(temperature != null && temperature > 37.3 ? 2 : 1); // 浣撴俯异常鍒ゆ柇
        record.setSecurityCheckResult(1); // 默认瀹夋通过
        record.setRemarks(remarks);
        
        visitorRecordMapper.insert(record);
        
        log.info("[recordVisitorEntry] 访客入园记录成功锛岃褰旾D锛歿}", record.getId());
        
        return record.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long recordVisitorExit(Long applicationId, Long operatorId, String gateLocation, String remarks) {
        log.info("[recordVisitorExit] 记录访客出园锛岀敵璇稩D锛歿}锛屾搷浣滃憳锛歿}锛岄棬宀楋細{}", 
                applicationId, operatorId, gateLocation);
        
        // 创建出园记录
        VisitorRecordDO record = new VisitorRecordDO();
        record.setApplicationId(applicationId);
        record.setOperationType(2); // 出园
        record.setOperationTime(LocalDateTime.now());
        record.setOperatorId(operatorId);
        record.setGateLocation(gateLocation);
        record.setVerificationMethod(1); // 默认二维码侀獙璇?        record.setHealthStatus(1); // 正常
        record.setSecurityCheckResult(1); // 通过
        record.setRemarks(remarks);
        
        visitorRecordMapper.insert(record);
        
        log.info("[recordVisitorExit] 访客出园记录成功锛岃褰旾D锛歿}", record.getId());
        
        return record.getId();
    }

    @Override
    public Long getCurrentVisitorCount() {
        return visitorRecordMapper.selectCurrentVisitorCount();
    }

    @Override
    public Object getVisitorRecordStatistics(LocalDateTime startTime, LocalDateTime endTime) {
        log.info("[getVisitorRecordStatistics] 鑾峰彇访客进出统计锛屾椂闂磋寖鍥达細{} - {}", startTime, endTime);

        // 杩欓噷返回涓€涓畝鍖栫殑统计瀵硅薄
        return new Object() {
            public final Long totalEntryCount = visitorRecordMapper.selectCountByTimeRange(startTime, endTime, 1);
            public final Long totalExitCount = visitorRecordMapper.selectCountByTimeRange(startTime, endTime, 2);
            public final Long currentVisitorCount = getCurrentVisitorCount();
        };
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void checkIn(AppVisitorCheckInReqVO reqVO) {
        log.info("[checkIn] 访客入园登记锛岀敵璇稩D锛歿}", reqVO.getApplicationId());

        // 创建入园记录
        VisitorRecordDO record = new VisitorRecordDO();
        record.setApplicationId(reqVO.getApplicationId());
        record.setOperationType(1); // 入园
        record.setOperationTime(LocalDateTime.now());
        record.setOperatorId(getLoginUserId());
        record.setGateLocation(reqVO.getGateLocation());
        record.setVerificationMethod(reqVO.getVerificationMethod());
        record.setVehiclePlate(reqVO.getVehiclePlate());
        record.setVehiclePhoto(reqVO.getVehiclePhoto());
        record.setVisitorPhoto(reqVO.getVisitorPhoto());
        record.setTemperature(reqVO.getTemperature());
        record.setHealthStatus(reqVO.getHealthStatus());
        record.setSecurityCheckResult(reqVO.getSecurityCheckResult());
        record.setAbnormalInfo(reqVO.getAbnormalInfo());
        record.setAbnormalPhotos(reqVO.getAbnormalPhotos());
        record.setRemarks(reqVO.getRemarks());

        visitorRecordMapper.insert(record);

        log.info("[checkIn] 访客入园登记成功锛岃褰旾D锛歿}", record.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void checkOut(AppVisitorCheckOutReqVO reqVO) {
        log.info("[checkOut] 访客出园登记锛岀敵璇稩D锛歿}", reqVO.getApplicationId());

        // 创建出园记录
        VisitorRecordDO record = new VisitorRecordDO();
        record.setApplicationId(reqVO.getApplicationId());
        record.setOperationType(2); // 出园
        record.setOperationTime(LocalDateTime.now());
        record.setOperatorId(getLoginUserId());
        record.setGateLocation(reqVO.getGateLocation());
        record.setVerificationMethod(1); // 默认二维码侀獙璇?        record.setVehiclePlate(reqVO.getVehiclePlate());
        record.setVehiclePhoto(reqVO.getVehiclePhoto());
        record.setVisitorPhoto(reqVO.getVisitorPhoto());
        record.setHealthStatus(1); // 正常
        record.setSecurityCheckResult(1); // 通过
        record.setAbnormalInfo(reqVO.getAbnormalInfo());
        record.setAbnormalPhotos(reqVO.getAbnormalPhotos());
        record.setRemarks(reqVO.getRemarks());

        visitorRecordMapper.insert(record);

        log.info("[checkOut] 访客出园登记成功锛岃褰旾D锛歿}", record.getId());
    }

}

