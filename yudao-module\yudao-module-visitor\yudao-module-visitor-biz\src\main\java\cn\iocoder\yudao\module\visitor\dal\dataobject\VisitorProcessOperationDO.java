package cn.iocoder.yudao.module.visitor.dal.dataobject;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.module.visitor.enums.VisitorOperationResultEnum;
import cn.iocoder.yudao.module.visitor.enums.VisitorOperationTypeEnum;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 访客娴佺▼操作员记录 DO
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@TableName(value = "visitor_process_operation", autoResultMap = true)
@KeySequence("visitor_process_operation_seq") // 用于 Oracle銆丳ostgreSQL銆並ingbase銆丏B2銆丠2 数据库撶殑主键自增銆傚鏋滄槸 MySQL 绛夋暟鎹簱锛屽彲涓嶅啓
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VisitorProcessOperationDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 申请鍗旾D
     */
    private Long applicationId;

    /**
     * Flowable娴佺▼实例ID
     */
    private String processInstanceId;

    /**
     * Flowable任务ID
     */
    private String taskId;

    /**
     * 任务瀹氫箟Key
     */
    private String taskDefinitionKey;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 操作员类型
     *
     * 鏋氫妇 {@link VisitorOperationTypeEnum}
     */
    private Integer operationType;

    /**
     * 操作员结果
     *
     * 鏋氫妇 {@link VisitorOperationResultEnum}
     */
    private Integer operationResult;

    /**
     * 操作员浜篒D
     */
    private Long operatorId;

    /**
     * 操作员浜哄鍚?     */
    private String operatorName;

    /**
     * 操作员人类型嬶細1-申请浜?2-联系人?3-瀹℃壒浜?4-璀﹀崼 5-绯荤粺鑷姩
     */
    private Integer operatorType;

    /**
     * 操作员时间
     */
    private LocalDateTime operationTime;

    /**
     * 操作员鑰楁椂锛堝垎閽燂級
     */
    private Integer operationDuration;

    /**
     * 操作员鍐呭描述
     */
    private String operationContent;

    /**
     * 操作员鍘熷洜/备注
     */
    private String operationReason;

    /**
     * 操作员鍓嶇姸鎬?     */
    private Integer beforeStatus;

    /**
     * 操作员鍚庣姸鎬?     */
    private Integer afterStatus;

    /**
     * 表ㄥ崟鏁版嵁蹇収
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> formData;

    /**
     * 闄勪欢URL数组
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> attachmentUrls;

    /**
     * 操作员IP地址
     */
    private String ipAddress;

    /**
     * 鐢ㄦ埛浠ｇ悊信息
     */
    private String userAgent;

    /**
     * 璁惧信息
     */
    private String deviceInfo;

    /**
     * 浣嶇疆信息
     */
    private String locationInfo;

}

