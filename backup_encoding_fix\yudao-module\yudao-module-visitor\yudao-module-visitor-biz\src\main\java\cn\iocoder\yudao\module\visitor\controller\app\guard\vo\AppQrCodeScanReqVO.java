package cn.iocoder.yudao.module.visitor.controller.app.guard.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;


/**
 * 用户 APP - 二维码扫描 Request VO
 *
 * <AUTHOR>
 * @since 2025-01-12
 */
@Schema(description = "用户 APP - 二维码扫描 Request VO")
@Data
public class AppQrCodeScanReqVO {

    @Schema(description = "二维码内容", requiredMode = Schema.RequiredMode.REQUIRED, example = "eyJhbGciOiJIUzI1NiJ9...")
    @NotBlank(message = "二维码内容不能为空")
    private String qrCodeContent;

    @Schema(description = "门岗位置", example = "东门")
    private String gateLocation;

    @Schema(description = "扫描设备ID", example = "GATE_001")
    private String deviceId;

}
