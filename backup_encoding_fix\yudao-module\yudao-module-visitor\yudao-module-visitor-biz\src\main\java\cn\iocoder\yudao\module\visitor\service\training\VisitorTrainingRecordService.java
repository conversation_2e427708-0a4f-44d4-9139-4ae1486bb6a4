package cn.iocoder.yudao.module.visitor.service.training;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.visitor.controller.admin.training.vo.VisitorTrainingRecordCreateReqVO;
import cn.iocoder.yudao.module.visitor.controller.admin.training.vo.VisitorTrainingRecordPageReqVO;
import cn.iocoder.yudao.module.visitor.controller.admin.training.vo.VisitorTrainingRecordUpdateReqVO;
import cn.iocoder.yudao.module.visitor.dal.dataobject.VisitorTrainingRecordDO;

import jakarta.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * 访客培训完成记录 Service 接口
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
public interface VisitorTrainingRecordService {

    /**
     * 创建访客培训完成记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createVisitorTrainingRecord(@Valid VisitorTrainingRecordCreateReqVO createReqVO);

    /**
     * 更新访客培训完成记录
     *
     * @param updateReqVO 更新信息
     */
    void updateVisitorTrainingRecord(@Valid VisitorTrainingRecordUpdateReqVO updateReqVO);

    /**
     * 删除访客培训完成记录
     *
     * @param id 编号
     */
    void deleteVisitorTrainingRecord(Long id);

    /**
     * 获得访客培训完成记录
     *
     * @param id 编号
     * @return 访客培训完成记录
     */
    VisitorTrainingRecordDO getVisitorTrainingRecord(Long id);

    /**
     * 获得访客培训完成记录列表
     *
     * @param ids 编号
     * @return 访客培训完成记录列表
     */
    List<VisitorTrainingRecordDO> getVisitorTrainingRecordList(Collection<Long> ids);

    /**
     * 获得访客培训完成记录分页
     *
     * @param pageReqVO 分页查询
     * @return 访客培训完成记录分页
     */
    PageResult<VisitorTrainingRecordDO> getVisitorTrainingRecordPage(VisitorTrainingRecordPageReqVO pageReqVO);

    /**
     * 获得访客培训完成记录列表, 鐢ㄤ簬 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 访客培训完成记录列表
     */
    List<VisitorTrainingRecordDO> getVisitorTrainingRecordList(VisitorTrainingRecordPageReqVO exportReqVO);

    /**
     * 开始嬪煿璁?     *
     * @param applicationId 申请ID
     * @param trainingId 培训ID
     * @param visitorName 访客姓名
     * @return 培训记录ID
     */
    Long startTraining(Long applicationId, Long trainingId, String visitorName);

    /**
     * 完成培训
     *
     * @param recordId 记录ID
     * @param examScore 考试分数
     * @param examAnswers 考试绛旀
     * @param signatureImage 绛惧瓧鍥剧墖
     */
    void completeTraining(Long recordId, Integer examScore, Object examAnswers, String signatureImage);

    /**
     * 检查查询瀹㈡槸鍚﹀畬鎴愭墍鏈夊繀淇煿璁?     *
     * @param applicationId 申请ID
     * @param visitorType 访客类型
     * @return 鏄惁完成
     */
    boolean isAllRequiredTrainingCompleted(Long applicationId, Integer visitorType);

    /**
     * 鑾峰彇访客培训进度
     *
     * @param applicationId 申请ID
     * @return 培训进度信息
     */
    Object getTrainingProgress(Long applicationId);

}

