package cn.iocoder.yudao.module.visitor.framework.flowable.listener;

import cn.iocoder.yudao.module.visitor.service.application.VisitorApplicationService;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

/**
 * 访客申请流程鍚姩鐩戝惉鍣? *
 * <AUTHOR>
 * @since 2025-01-12
 */
@Component("visitorApplicationStartListener")
@Slf4j
public class VisitorApplicationStartListener implements ExecutionListener {

    @Resource
    private VisitorApplicationService visitorApplicationService;

    @Override
    public void notify(DelegateExecution execution) {
        log.info("[notify] 访客申请流程鍚姩锛屾祦绋嬪疄渚婭D锛歿}", execution.getProcessInstanceId());
        
        try {
            // 鑾峰彇涓氬姟Key锛堢敵璇峰崟鍙凤級
            String businessKey = execution.getProcessInstanceBusinessKey();
            log.info("[notify] 涓氬姟Key锛歿}", businessKey);
            
            // 鑾峰彇流程鍙橀噺
            Object applicationIdObj = execution.getVariable("applicationId");
            if(applicationIdObj != null) {
                Long applicationId = Long.valueOf(applicationIdObj.toString());
                log.info("[notify] 申请ID锛歿}", applicationId);
                
                // 更新申请状态佷负流程涓?                // visitorApplicationService.updateProcessStatus(applicationId, execution.getProcessInstanceId());
            }

            log.info("[notify] 访客申请流程鍚姩鐩戝惉鍣ㄦ墽表屽畬鎴?);

        } catch(Exception e) {
            log.error("[notify] 访客申请流程鍚姩鐩戝惉鍣ㄦ墽表屽け璐?, e);
            throw new RuntimeException("流程鍚姩鐩戝惉鍣ㄦ墽表屽け璐?, e);
        }
    }

}

