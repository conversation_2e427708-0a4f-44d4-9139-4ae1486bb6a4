package cn.iocoder.yudao.module.visitor.dal.dataobject;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 访客进出记录 DO
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@TableName(value = "visitor_record", autoResultMap = true)
@KeySequence("visitor_record_seq") // 用于 Oracle銆丳ostgreSQL銆並ingbase銆丏B2銆丠2 数据库撶殑主键自增銆傚鏋滄槸 MySQL 绛夋暟鎹簱锛屽彲涓嶅啓
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VisitorRecordDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 申请鍗旾D
     */
    private Long applicationId;

    /**
     * 访客姓名
     */
    private String visitorName;

    /**
     * 访客电话
     */
    private String visitorPhone;

    /**
     * 操作员类型锛?-入园 2-出园
     */
    private Integer operationType;

    /**
     * 操作员时间
     */
    private LocalDateTime operationTime;

    /**
     * 操作员浜篒D
     */
    private Long operatorId;

    /**
     * 操作员姓名鍚?     */
    private String operatorName;

    /**
     * 门岗位置
     */
    private String gateLocation;

    /**
     * 验证方式锛?-二维码?2-鎵嬪姩 3-浜鸿劯璇嗗埆
     */
    private Integer verificationMethod;

    /**
     * 车牌?     */
    private String vehiclePlate;

    /**
     * 车辆照片URL
     */
    private String vehiclePhoto;

    /**
     * 鐜板満访客照片URL
     */
    private String visitorPhoto;

    /**
     * 异常鎯呭喌描述
     */
    private String abnormalInfo;

    /**
     * 异常照片URL数组
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> abnormalPhotos;

    /**
     * 浣撴俯锛堟憚姘忓害锛?     */
    private BigDecimal temperature;

    /**
     * 健康状态侊細1-正常 2-异常
     */
    private Integer healthStatus;

    /**
     * 瀹夋结果锛?-通过 2-鏈€氳繃
     */
    private Integer securityCheckResult;

    /**
     * 备注信息
     */
    private String remarks;

}



