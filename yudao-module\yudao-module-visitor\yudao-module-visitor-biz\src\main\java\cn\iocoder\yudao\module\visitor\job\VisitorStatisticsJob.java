package cn.iocoder.yudao.module.visitor.job;

import cn.iocoder.yudao.framework.quartz.core.handler.JobHandler;
import cn.iocoder.yudao.framework.tenant.core.job.TenantJob;
import cn.iocoder.yudao.module.visitor.service.application.VisitorApplicationService;
import cn.iocoder.yudao.module.visitor.service.record.VisitorRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 访客统计数据定时任务
 *
 * <AUTHOR>
 * @since 2025-01-12
 */
@Component
@Slf4j
public class VisitorStatisticsJob implements JobHandler {

    @Resource
    private VisitorApplicationService visitorApplicationService;

    @Resource
    private VisitorRecordService visitorRecordService;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    @TenantJob
    public String execute(String param) throws Exception {
        log.info("[execute] 开始执行访客统计数据定时任务");
        
        try {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime startTime = now.withHour(0).withMinute(0).withSecond(0).withNano(0);
            LocalDateTime endTime = now.withHour(23).withMinute(59).withSecond(59).withNano(999999999);
            
            // 统计今日访客申请数据
            Object applicationStats = visitorApplicationService.getVisitorApplicationStatistics(startTime, endTime);
            log.info("[execute] 今日访客申请统计：{}", applicationStats);
            
            // 统计今日访客进出数据
            Object recordStats = visitorRecordService.getVisitorRecordStatistics(startTime, endTime);
            log.info("[execute] 今日访客进出统计：{}", recordStats);
            
            // 获取当前在园访客数量
            Long currentCount = visitorRecordService.getCurrentVisitorCount();
            log.info("[execute] 当前在园访客数量：{}", currentCount);
            
            // 将统计数据保存到缓存中，供前端展示使用
            saveStatisticsToCache(applicationStats, recordStats, currentCount);
            
            log.info("[execute] 访客统计数据定时任务执行完成");
            return "访客统计数据更新完成";
            
        } catch(Exception e) {
            log.error("[execute] 访客统计数据定时任务执行失败", e);
            throw e;
        }
    }

    /**
     * 保存统计数据到缓存
     */
    private void saveStatisticsToCache(Object applicationStats, Object recordStats, Long currentCount) {
        try {
            // 构建统计数据
            Map<String, Object> todayStats = new HashMap<>();
            todayStats.put("applicationStats", applicationStats);
            todayStats.put("recordStats", recordStats);
            todayStats.put("currentCount", currentCount);
            todayStats.put("updateTime", LocalDateTime.now().toString());

            // 保存到Redis，缓存1小时
            String cacheKey = "visitor:stats:today";
            redisTemplate.opsForValue().set(cacheKey, todayStats, Duration.ofHours(1));

            log.info("[saveStatisticsToCache] 统计数据已保存到缓存，key：{}", cacheKey);

        } catch(Exception e) {
            log.error("[saveStatisticsToCache] 保存统计数据到缓存失败", e);
        }
    }

}

