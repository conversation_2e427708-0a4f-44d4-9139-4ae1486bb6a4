package cn.iocoder.yudao.module.visitor.dal.mysql;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.visitor.dal.dataobject.VisitorStatusHistoryDO;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 访客状态佸彉鏇村巻鍙?Mapper
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
@Mapper
public interface VisitorStatusHistoryMapper extends BaseMapperX<VisitorStatusHistoryDO> {

    default List<VisitorStatusHistoryDO> selectListByApplicationId(Long applicationId) {
        return selectList(new LambdaQueryWrapperX<VisitorStatusHistoryDO>()
                .eq(VisitorStatusHistoryDO::getApplicationId, applicationId)
                .orderByAsc(VisitorStatusHistoryDO::getChangeSequence));
    }

    default VisitorStatusHistoryDO selectLatestByApplicationId(Long applicationId) {
        return selectOne(new LambdaQueryWrapperX<VisitorStatusHistoryDO>()
                .eq(VisitorStatusHistoryDO::getApplicationId, applicationId)
                .orderByDesc(VisitorStatusHistoryDO::getChangeSequence)
                .last("LIMIT 1"));
    }

    default Integer selectMaxSequenceByApplicationId(Long applicationId) {
        VisitorStatusHistoryDO latest = selectLatestByApplicationId(applicationId);
        return latest != null ? latest.getChangeSequence() : 0;
    }

    default List<VisitorStatusHistoryDO> selectListByProcessInstanceId(String processInstanceId) {
        return selectList(new LambdaQueryWrapperX<VisitorStatusHistoryDO>()
                .eq(VisitorStatusHistoryDO::getProcessInstanceId, processInstanceId)
                .orderByAsc(VisitorStatusHistoryDO::getChangeSequence));
    }

    default Long selectCountByStatusChange(Integer beforeStatus, Integer afterStatus, LocalDateTime startTime, LocalDateTime endTime) {
        return selectCount(new LambdaQueryWrapperX<VisitorStatusHistoryDO>()
                .eq(VisitorStatusHistoryDO::getBeforeStatus, beforeStatus)
                .eq(VisitorStatusHistoryDO::getAfterStatus, afterStatus)
                .ge(VisitorStatusHistoryDO::getChangeTime, startTime)
                .le(VisitorStatusHistoryDO::getChangeTime, endTime));
    }

    default Long selectCountByAfterStatus(Integer afterStatus, LocalDateTime startTime, LocalDateTime endTime) {
        return selectCount(new LambdaQueryWrapperX<VisitorStatusHistoryDO>()
                .eq(VisitorStatusHistoryDO::getAfterStatus, afterStatus)
                .ge(VisitorStatusHistoryDO::getChangeTime, startTime)
                .le(VisitorStatusHistoryDO::getChangeTime, endTime));
    }

    default Double selectAvgDurationByStatusChange(Integer beforeStatus, Integer afterStatus, LocalDateTime startTime, LocalDateTime endTime) {
        // 杩欓噷闇€瑕佸鏉傜殑SQL查询鏉ヨ绠楀钩鍧囨椂闀匡紝鏆傛椂杩斿洖0.0
        // 瀹為檯实现类闇€瑕佹牴鎹叿浣撶殑鏁版嵁搴撳拰涓氬姟閫昏緫鏉ョ紪鍐?        return 0.0;
    }

}

