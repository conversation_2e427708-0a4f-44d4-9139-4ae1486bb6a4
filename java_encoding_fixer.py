#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Java文件编码修复脚本
解析FIX_NOTE.txt文件，修复Java文件的编码问题和乱码
确保所有文件使用UTF-8编码（无BOM）
"""

import json
import os
import shutil
import chardet
import re
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Tuple, Optional

class JavaEncodingFixer:
    def __init__(self, workspace_root: str):
        """
        初始化Java编码修复器
        
        Args:
            workspace_root: 工作区根目录路径
        """
        self.workspace_root = Path(workspace_root)
        self.backup_dir = self.workspace_root / "backup_encoding_fix"
        self.backup_dir.mkdir(exist_ok=True)
        
        # 创建日志文件
        self.log_file = self.workspace_root / "encoding_fix.log"
        
        # 乱码修复映射表 - 常见的UTF-8到GBK转换错误
        self.garbled_mappings = {
            # 基础中文字符乱码
            '娴佺▼': '流程',
            '鍗旾D': '单ID', 
            '缁撴灉': '结果',
            '浜篒D': '人ID',
            '浜哄鍚?': '人姓名',
            '浜虹被鍨?': '人类型',
            '浜哄鍚嶇О': '人姓名',
            '浜虹被鍨嬪瀷': '人类型',
            '鎿嶄綔鍛?': '操作员',
            '鎿嶄綔': '操作',
            '璁板綍': '记录',
            '鍒嗛〉': '分页',
            '绠＄悊': '管理',
            '鍚庡彴': '后台',
            '璁块': '访客',
            '瀹炰緥': '实例',
            '浠诲姟': '任务',
            '绫诲瀷': '类型',
            '鏃堕棿': '时间',
            '鍒涘缓': '创建',
            '鏇存柊': '更新',
            '鍒犻櫎': '删除',
            '鏌ヨ': '查询',
            '寮犱笁': '张三',
            '鐢ㄦ埛': '用户',
            '绯荤粺': '系统',
            '閰嶇疆': '配置',
            '鏁版嵁': '数据',
            '搴撹〃': '库表',
            '瀛楁': '字段',
            '绱㈠紩': '索引',
            '鍏抽敭': '关键',
            '涓婚敭': '主键',
            '澶栭敭': '外键',
            '鍞竴': '唯一',
            '闈炵┖': '非空',
            '榛樿': '默认',
            '鑷姩': '自动',
            '澧為暱': '增长',
            '娉ㄩ噴': '注释',
            '鎻忚堪': '描述',
            '绀轰緥': '示例',
            '鍙傛暟': '参数',
            '杩斿洖': '返回',
            '鍊?': '值',
            '鏍煎紡': '格式',
            '鏍￠獙': '校验',
            '蹇呭～': '必填',
            '鍙€?': '可选',
            '鏈€澶?': '最大',
            '鏈€灏?': '最小',
            '闀垮害': '长度'
        }
        
        # 统计信息
        self.stats = {
            'total_files': 0,
            'success_files': 0,
            'failed_files': 0,
            'skipped_files': 0,
            'garbled_fixed': 0
        }
        
        # 失败文件列表
        self.failed_files = []
        
    def log(self, message: str, level: str = "INFO"):
        """记录日志"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] [{level}] {message}"
        print(log_entry)
        
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(log_entry + '\n')
    
    def parse_fix_note(self, fix_note_path: str) -> List[str]:
        """
        解析FIX_NOTE.txt文件，提取Java文件路径
        
        Args:
            fix_note_path: FIX_NOTE.txt文件路径
            
        Returns:
            Java文件相对路径列表
        """
        self.log("开始解析FIX_NOTE.txt文件")
        
        try:
            with open(fix_note_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 使用正则表达式提取resource路径
            pattern = r'"resource":\s*"([^"]+)"'
            matches = re.findall(pattern, content)
            
            java_files = set()
            workspace_prefix = '/D:/work/code/ptl/zc/ruoyi-vue-pro-yunqu-park/'
            
            for match in matches:
                # 过滤Java文件
                if match.endswith('.java') and match.startswith(workspace_prefix):
                    relative_path = match.replace(workspace_prefix, '')
                    # 转换为Windows路径分隔符
                    relative_path = relative_path.replace('/', os.sep)
                    java_files.add(relative_path)
            
            java_files_list = sorted(list(java_files))
            self.log(f"成功解析出 {len(java_files_list)} 个Java文件")
            
            return java_files_list
            
        except Exception as e:
            self.log(f"解析FIX_NOTE.txt失败: {e}", "ERROR")
            return []
    
    def detect_file_encoding(self, file_path: Path) -> str:
        """
        检测文件编码
        
        Args:
            file_path: 文件路径
            
        Returns:
            检测到的编码格式
        """
        try:
            with open(file_path, 'rb') as f:
                raw_data = f.read()
            
            # 检测BOM
            if raw_data.startswith(b'\xef\xbb\xbf'):
                return 'utf-8-sig'
            elif raw_data.startswith(b'\xff\xfe'):
                return 'utf-16-le'
            elif raw_data.startswith(b'\xfe\xff'):
                return 'utf-16-be'
            
            # 使用chardet检测
            result = chardet.detect(raw_data)
            detected_encoding = result['encoding'] if result['encoding'] else 'utf-8'
            confidence = result['confidence'] if result['confidence'] else 0
            
            self.log(f"检测编码: {detected_encoding} (置信度: {confidence:.2f})")
            return detected_encoding
            
        except Exception as e:
            self.log(f"编码检测失败: {e}", "ERROR")
            return 'utf-8'
    
    def read_file_content(self, file_path: Path) -> Optional[str]:
        """
        读取文件内容，尝试多种编码
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件内容字符串，失败返回None
        """
        # 检测编码
        detected_encoding = self.detect_file_encoding(file_path)
        
        # 尝试的编码列表
        encodings_to_try = [
            detected_encoding,
            'utf-8',
            'utf-8-sig', 
            'gbk',
            'gb2312',
            'gb18030',
            'big5',
            'latin1'
        ]
        
        # 去重并保持顺序
        encodings_to_try = list(dict.fromkeys(encodings_to_try))
        
        for encoding in encodings_to_try:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    content = f.read()
                self.log(f"成功使用编码 {encoding} 读取文件")
                return content
            except UnicodeDecodeError:
                continue
            except Exception as e:
                self.log(f"使用编码 {encoding} 读取失败: {e}", "WARNING")
                continue
        
        self.log(f"所有编码尝试失败", "ERROR")
        return None
    
    def fix_garbled_text(self, content: str) -> Tuple[str, bool]:
        """
        修复乱码文本
        
        Args:
            content: 原始文件内容
            
        Returns:
            (修复后的内容, 是否有修复)
        """
        original_content = content
        has_fixes = False
        
        # 应用乱码映射
        for garbled, correct in self.garbled_mappings.items():
            if garbled in content:
                content = content.replace(garbled, correct)
                has_fixes = True
                self.log(f"修复乱码: '{garbled}' -> '{correct}'")
        
        # 修复特殊的乱码模式
        # 修复引号和描述问题
        patterns = [
            (r'浜哄鍚?\s*,\s*example\s*=\s*"([^"]*)"', r'人姓名", example = "\1"'),
            (r'浜虹被鍨?\s*,\s*example\s*=\s*"([^"]*)"', r'人类型", example = "\1"'),
            (r'description\s*=\s*"([^"]*浜哄鍚?[^"]*)"', r'description = "操作员人姓名"'),
            (r'description\s*=\s*"([^"]*浜虹被鍨?[^"]*)"', r'description = "操作员人类型"'),
        ]
        
        for pattern, replacement in patterns:
            new_content = re.sub(pattern, replacement, content)
            if new_content != content:
                content = new_content
                has_fixes = True
                self.log(f"修复模式: {pattern}")
        
        if has_fixes:
            self.stats['garbled_fixed'] += 1

        return content, has_fixes

    def backup_file(self, file_path: Path) -> Optional[Path]:
        """
        备份文件

        Args:
            file_path: 要备份的文件路径

        Returns:
            备份文件路径，失败返回None
        """
        try:
            # 创建备份文件路径，保持目录结构
            relative_path = file_path.relative_to(self.workspace_root)
            backup_path = self.backup_dir / relative_path

            # 创建备份目录
            backup_path.parent.mkdir(parents=True, exist_ok=True)

            # 如果备份文件已存在，添加时间戳
            if backup_path.exists():
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_path = backup_path.with_name(f"{backup_path.stem}_{timestamp}{backup_path.suffix}")

            # 复制文件
            shutil.copy2(file_path, backup_path)
            self.log(f"备份文件: {file_path} -> {backup_path}")

            return backup_path

        except Exception as e:
            self.log(f"备份文件失败 {file_path}: {e}", "ERROR")
            return None

    def validate_java_syntax(self, content: str) -> bool:
        """
        简单的Java语法验证

        Args:
            content: Java文件内容

        Returns:
            是否通过基本语法检查
        """
        # 基本的语法检查
        checks = [
            # 检查括号匹配
            lambda c: c.count('(') == c.count(')'),
            lambda c: c.count('{') == c.count('}'),
            lambda c: c.count('[') == c.count(']'),
            # 检查引号匹配（简单检查）
            lambda c: c.count('"') % 2 == 0,
            # 检查是否包含基本的Java结构
            lambda c: 'class ' in c or 'interface ' in c or 'enum ' in c,
        ]

        for check in checks:
            try:
                if not check(content):
                    return False
            except:
                return False

        return True

    def process_single_file(self, relative_path: str) -> bool:
        """
        处理单个Java文件

        Args:
            relative_path: 文件相对路径

        Returns:
            是否处理成功
        """
        file_path = self.workspace_root / relative_path

        if not file_path.exists():
            self.log(f"文件不存在: {file_path}", "WARNING")
            self.stats['skipped_files'] += 1
            return False

        self.log(f"开始处理文件: {relative_path}")

        try:
            # 1. 备份原文件
            backup_path = self.backup_file(file_path)
            if not backup_path:
                self.failed_files.append(relative_path)
                self.stats['failed_files'] += 1
                return False

            # 2. 读取文件内容
            content = self.read_file_content(file_path)
            if content is None:
                self.log(f"无法读取文件内容: {file_path}", "ERROR")
                self.failed_files.append(relative_path)
                self.stats['failed_files'] += 1
                return False

            # 3. 修复乱码
            fixed_content, has_fixes = self.fix_garbled_text(content)

            # 4. 验证语法
            if not self.validate_java_syntax(fixed_content):
                self.log(f"语法验证失败: {file_path}", "WARNING")
                # 继续处理，但记录警告

            # 5. 删除原文件
            os.remove(file_path)
            self.log(f"删除原文件: {file_path}")

            # 6. 重新创建文件，使用UTF-8编码（无BOM）
            with open(file_path, 'w', encoding='utf-8', newline='') as f:
                f.write(fixed_content)

            self.log(f"重新生成文件（UTF-8编码）: {file_path}")

            if has_fixes:
                self.log(f"文件已修复乱码: {relative_path}")

            self.stats['success_files'] += 1
            return True

        except Exception as e:
            self.log(f"处理文件失败 {relative_path}: {e}", "ERROR")

            # 尝试恢复备份
            if backup_path and backup_path.exists():
                try:
                    shutil.copy2(backup_path, file_path)
                    self.log(f"已恢复备份: {file_path}")
                except:
                    self.log(f"恢复备份失败: {file_path}", "ERROR")

            self.failed_files.append(relative_path)
            self.stats['failed_files'] += 1
            return False

    def generate_report(self) -> str:
        """
        生成处理报告

        Returns:
            报告内容
        """
        report_lines = [
            "# Java文件编码修复报告",
            "",
            f"## 处理概要",
            f"- **处理时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"- **总文件数**: {self.stats['total_files']}",
            f"- **成功处理**: {self.stats['success_files']}",
            f"- **处理失败**: {self.stats['failed_files']}",
            f"- **跳过文件**: {self.stats['skipped_files']}",
            f"- **修复乱码**: {self.stats['garbled_fixed']}",
            "",
            f"## 编码规范",
            f"- 所有文件使用UTF-8编码",
            f"- 无BOM（Byte Order Mark）标记",
            f"- 中文字符正确显示",
            "",
            f"## 备份信息",
            f"- 备份目录: {self.backup_dir}",
            f"- 所有原始文件已备份，可随时恢复",
            "",
        ]

        if self.failed_files:
            report_lines.extend([
                "## 失败文件列表",
                ""
            ])
            for failed_file in self.failed_files:
                report_lines.append(f"- {failed_file}")
            report_lines.append("")

        report_lines.extend([
            "## 乱码修复映射",
            "| 乱码字符 | 正确字符 |",
            "|----------|----------|"
        ])

        for garbled, correct in list(self.garbled_mappings.items())[:20]:  # 显示前20个
            report_lines.append(f"| {garbled} | {correct} |")

        if len(self.garbled_mappings) > 20:
            report_lines.append(f"| ... | ... |")
            report_lines.append(f"*共 {len(self.garbled_mappings)} 个映射规则*")

        return '\n'.join(report_lines)

    def process_all_files(self, fix_note_path: str):
        """
        处理所有文件

        Args:
            fix_note_path: FIX_NOTE.txt文件路径
        """
        self.log("=" * 60)
        self.log("开始Java文件编码修复任务")
        self.log("=" * 60)

        # 解析文件列表
        java_files = self.parse_fix_note(fix_note_path)

        if not java_files:
            self.log("未找到需要处理的Java文件", "WARNING")
            return

        self.stats['total_files'] = len(java_files)
        self.log(f"找到 {len(java_files)} 个Java文件需要处理")

        # 处理每个文件
        for i, java_file in enumerate(java_files, 1):
            self.log(f"处理进度: {i}/{len(java_files)}")
            self.process_single_file(java_file)

        # 生成报告
        report_content = self.generate_report()
        report_path = self.workspace_root / "encoding_fix_report.md"

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)

        self.log("=" * 60)
        self.log("Java文件编码修复任务完成")
        self.log(f"详细报告已保存: {report_path}")
        self.log(f"日志文件: {self.log_file}")
        self.log("=" * 60)

        # 打印统计信息
        print("\n" + "=" * 50)
        print("处理结果统计:")
        print(f"总文件数: {self.stats['total_files']}")
        print(f"成功处理: {self.stats['success_files']}")
        print(f"处理失败: {self.stats['failed_files']}")
        print(f"跳过文件: {self.stats['skipped_files']}")
        print(f"修复乱码: {self.stats['garbled_fixed']}")
        print("=" * 50)


def main():
    """主函数"""
    workspace_root = r"d:\work\code\ptl\zc\ruoyi-vue-pro-yunqu-park"
    fix_note_path = os.path.join(workspace_root, "issues", "FIX_NOTE.txt")

    # 检查文件是否存在
    if not os.path.exists(fix_note_path):
        print(f"错误: FIX_NOTE.txt文件不存在: {fix_note_path}")
        return

    # 创建修复器并执行
    fixer = JavaEncodingFixer(workspace_root)
    fixer.process_all_files(fix_note_path)


if __name__ == "__main__":
    main()
